\documentclass[twoside,11pt]{article}

%================================ PREAMBLE ==================================

%--------- Packages -----------
\usepackage{fullpage}
\usepackage{amssymb}
\usepackage{amsmath}
\usepackage{multicol}
\usepackage{amsthm}
\usepackage{latexsym}
\usepackage{graphicx}
\usepackage{color}
\usepackage{url}
\usepackage{tikz}
\usepackage{listings}
\usepackage{fancyhdr}
\lstset{ basicstyle=\ttfamily, columns=fullflexible, frame=single, breaklines=true, postbreak=\mbox{\textcolor{red}{$\rightarrow$}\space} }

%----------Spacing-------------
\setlength{\topmargin}{-0.6 in}
\setlength{\headsep}{0.75 in}
\setlength{\parindent}{0 in}
\setlength{\parskip}{0.1 in}
\setlength{\headheight}{13.6pt}
\newcommand{\hide}[1]{}
\newcommand{\boxheader}[1]{
    \pagestyle{fancy}
    \thispagestyle{plain}
    \newpage
    \setcounter{page}{1}
    \noindent
    \begin{center}
        \framebox{
            \vbox{
                \vspace{2mm}
                \hbox to 6.28in { {\bf CIT 5960 Online} \hfill {\bf Summer 2025} }
                \vspace{2mm}
                \hbox to 6.28in { \hfill {\Large Module #1} \hfill }
                \vspace{2mm}
                \hbox to 6.28in { Name: Sey Kim | Collaborator: Not Applicable \hfill }
                \vspace{2mm}
            }
        }
    \end{center}
    \lhead{Name: Sey Kim | Collaborator: Not Applicable}
}


%---------Commands-----------
\renewcommand{\Pr}[1]{{\text{Pr}\left[ #1 \right]}}
\newcommand{\E}[1]{{\mathbf{E}\left[ #1 \right]}}
\newcommand{\Var}[1]{{\text{Var}\left( #1 \right)}}
\DeclareMathOperator*{\argmax}{arg\,max}
\DeclareMathOperator*{\argmin}{arg\,min}
\allowdisplaybreaks

%------Solutions Toggle------
\newcommand{\sol}[1]{{\par{}\noindent\rule{\textwidth}{0.5pt}\par\textbf{SOLUTION: } \par{} #1 \par{}\noindent\rule{\textwidth}{0.5pt}}}
% \newcommand{\sol}[1]{}

%=============================== END PREAMBLE ===============================

%============================ BEGIN DOCUMENT ================================

\begin{document}
\boxheader{1--2}

\begin{enumerate}
\item
    Rank the following functions in increasing order of asymptotic growth rate. That is, list the functions such that if $f(n)$ occurs before $g(n)$ in your list, then $f(n) = O(g(n))$. Remember that in this class, $\log$ with no subscript denotes the base-2 logarithm.

	If consecutive functions in your order are big-$\Theta$ of each other, indicate this in your solution as well. Please provide a brief explanation as to why you ordered each of the functions in the manner you did.\hfill\textbf{(20 points)}

    \begin{multicols}{3}
    \begin{enumerate}
    \item $n$
    \item $n^{1.5}$
    \item $n\log n$
    \item $\ln n$
    \item $\log(n^5)$
    \item $15000$
    \item $2^n$
    \item $2^{2n}$
    \item $3^n$
    \item $(n+1)^2$
    \item $\log(2^n)$
    \item $2^{n + 1}$
	\end{enumerate}
	\end{multicols}

\sol{
The functions in increasing order of asymptotic growth rate:

\begin{enumerate}
\item[(f)] $15000$
\item[(d,e)] $\ln n = \Theta(\log(n^5))$
\item[(a,k)] $n = \Theta(\log(2^n))$
\item[(c)] $n\log n$
\item[(b)] $n^{1.5}$
\item[(j)] $(n+1)^2$
\item[(g,l)] $2^n = \Theta(2^{n+1})$
\item[(i)] $3^n$
\item[(h)] $2^{2n}$
\end{enumerate}

\textit{Brief Explanation:}
\begin{enumerate}
\item[(f)] $15000$: Any constant function has the slowest asymptotic growth rate as it remains unchanged as $n$ increases

\item[(d,e)] $\ln n$ and $\log(n^5)$ are $\Theta$ of each other because $\log(n^5) = 5\log(n)$, and the difference between natural logarithm and base-2 logarithm is only a constant factor

\item[(a,k)] $n$ and $\log(2^n)$ are $\Theta$ of each other since $\log(2^n) = n\log(2) = n$ by the logarithm property.

\item[(c)] $n\log n$ grows faster than linear functions but slower than polynomial functions with exponent greater than 1

\item[(b)] $n^{1.5}$ grows faster than $n\log n$ because any polynomial function with exponent greater than 1 grows asymptotically faster than $n\log n$

\item[(j)] $(n+1)^2 = n^2 + 2n + 1 = \Theta(n^2)$ grows faster than $n^{1.5}$ but slower than exponential functions.

\item[(g,l)] $2^n$ and $2^{n+1} = 2 \cdot 2^n$ are $\Theta$ of each other as they differ only by a constant multiplicative factor

\item[(i)] $3^n$ grows faster than $2^n$ because $3 > 2$, making the exponential growth rate higher

\item[(h)] $2^{2n} = (2^2)^n = 4^n$ grows faster than $3^n$ because $4 > 3$, resulting in a higher exponential growth rate
\end{enumerate}
}

% QUESTION 2
\item
    Using big-$\Theta$ notation, give the running time of the following code snippets.

    \hfill\textbf{(5 + 5 + 10 points )}
    \begin{enumerate}
    \item
\begin{verbatim}
int sum = 0;
for (int i = 0; i < n; ++i)
    for (int j = n; j > i; --j)
        ++sum;
\end{verbatim}
    \item
\begin{verbatim}
int sum = 0;
for (int i = 2; i <= n; ++i)
    for (int j = 1; j <= n; j = j*2)
        ++sum;
\end{verbatim}
    \item
\begin{verbatim}
int sum = 0;
for (int i = 1; i <= n; ++i)
    for (int j = i; j <= n; ++j)
        for (int k = i; k <= j; ++k)
            ++sum;
\end{verbatim}
\end{enumerate}

% SOLUTION for Question 2
\sol{
\begin{enumerate}
  \item The outer loop runs for each \(i\) from \(0\) to \(n-1\), and for every such \(i\) the inner loop executes \(n-i\) times. Summing over all \(i\) gives \(\sum_{i=0}^{n-1}(n-i)=\frac{n(n+1)}{2}\). Ignoring the constant factor and lower-order term, the growth is quadratic:
        \[
          \boxed{\Theta(n^{2})}
        \]
  \item The outer loop iterates \(n-1\) times. Inside it, \(j\) doubles each step until it exceeds \(n\), so the inner loop runs \(\lfloor\log_2 n\rfloor+1=\Theta(\log n)\) times. Multiplying the two factors yields a linearithmic total:
        \[
          \boxed{\Theta(n\log n)}
        \]
  \item For each \(i\) from \(1\) to \(n\), the middle loop runs \(n-i+1\) times, and for each pair \((i,j)\), the inner loop runs \(j-i+1\) times. Evaluating the triple sum
        \[
          \sum_{i=1}^{n}\sum_{j=i}^{n}\sum_{k=i}^{j}1
          \;=\;\frac{n(n+1)(n+2)}{6},
        \]
        the dominant term is cubic:
        \[
          \boxed{\Theta(n^{3})}
        \]
\end{enumerate}}

% QUESTION 3
\item
Prove or disprove the statements below. To disprove a statement, it suffices to give a counterexample.\hspace*{\fill}({\bf 10 + 10 points})
\begin{enumerate}
    \item Suppose $f(n) = O(g(n))$. Then it must be the case that $2^{f(n)} = O(2^{g(n)})$.

    \item Suppose $f(n) = O(g(n))$. Then it must be the case that $(f(n))^2 = O(g(n)^2)$.
\end{enumerate}

% SOLUTION
\sol{
\begin{enumerate}
 \item \textbf{False.} \\
        Take \(f(n)=2n\) and \(g(n)=n\).  Then
        \[
          f(n)\le 2\,g(n)\quad\text{for all }n\ge1,
        \]
        so \(f(n)=O\bigl(g(n)\bigr)\).  On the other hand,
        \[
          \frac{2^{f(n)}}{2^{g(n)}} \;=\;\frac{4^n}{2^n} \;=\;2^n.
        \]
        Since \(2^n\) eventually exceeds any fixed constant, for every \(C>0\)
        there exists \(N\) such that for all \(n\ge N\), \(2^n > C\).  Hence
        \(2^{f(n)}>C\,2^{g(n)}\) for arbitrarily large \(n\), which shows
        \(2^{f(n)}\notin O\bigl(2^{g(n)}\bigr)\).

  \item \textbf{True.} \\
        If \(f(n)=O\bigl(g(n)\bigr)\), then there are constants \(c>0\) and
        \(n_{0}\) so that
        \[
          |f(n)|\le c\,|g(n)|\quad\text{for all }n\ge n_{0}.
        \]
        Squaring both sides gives
        \[
          |f(n)|^2 \;\le\; c^2\,|g(n)|^2,
        \]
        and therefore
        \[
          f(n)^2 = O\bigl(g(n)^2\bigr).
        \]
\end{enumerate}
}


% QUESTION 4
\item When we analyzed the Towers of Hanoi algorithm, we counted the number of moves of individual rings, implicitly assuming that this operation takes constant time. Suppose instead that the time it takes to move a single ring is equal to the size of the ring, meaning that the $i$\textsuperscript{th}-smallest ring takes $i$ steps to move, for each $1\leq i\leq n$. Write the resulting recurrence for the running time of the algorithm, and solve that recurrence.

You may utilize the following identity in your analysis without giving a proof: $$\sum_{i=1}^{K} i 2^i = (K-1)2^{K+1} + 2.$$
\hfill\textbf{(20 points)}
% SOLUTION Question 4
\\
\sol{
We analyze the running time of the Towers of Hanoi when moving the $i$-th smallest ring costs $i$ steps.  
Let $T(n)$ be the total number of steps needed to move $n$ rings.

Recurrence: moving $n$ rings requires
\[
T(n)=T(n-1)+n+T(n-1)=2T(n-1)+n,\qquad T(1)=1.
\]

Solving the recurrence, unrolling gives
\[
T(n)=\sum_{i=1}^{n} i\,2^{\,n-i}.
\]
Substitute $j=n-i$:
\[
T(n)=\sum_{j=0}^{n-1}(n-j)2^{j}
      =n\sum_{j=0}^{n-1}2^{j}-\sum_{j=0}^{n-1}j\,2^{j}.
\]
The first sum is $n(2^{n}-1)$; for the second, apply the given identity with $K=n-1$:
\[
\sum_{j=1}^{n-1} j\,2^{j}=(n-2)2^{n}+2.
\]
Substituting and simplifying,
\[
T(n)=2^{\,n+1}-n-2.
\]
\[
\boxed{T(n)=2^{\,n+1}-n-2=\Theta(2^{n}).}
\]

The exponential term $2^{\,n+1}$ grows much faster than the linear term $n$ as $n$ increases, making the overall time complexity exponential.
}

% Question 5
\item
Given two $n\times n$ matrices
\[X=
    \begin{bmatrix}
       x_{1,1}&\cdots&x_{1,n}\\
       \vdots&\ddots&\vdots\\
       x_{n,1}&\cdots&x_{n,n}
    \end{bmatrix}
    \quad\text{and}\quad
    Y=\begin{bmatrix}
        y_{1,1}&\cdots&y_{1,n}\\
        \vdots&\ddots&\vdots\\
        y_{n,1}&\cdots&y_{n,n}
    \end{bmatrix},
\]
their product is given by
\[XY=
    \begin{bmatrix}
        z_{1,1}&\cdots&z_{1,n}\\
        \vdots&\ddots&\vdots\\
        z_{n,1}&\cdots&z_{n,n}
    \end{bmatrix},
\]
where
\[z_{i,j}=\sum_{k=1}^n x_{i,k}y_{k,j}.\]
In other words, $z_{i,j}$ is the dot product of the $i$\textsuperscript{th} row of $X$ and the $j$\textsuperscript{th} column of $Y$. Suppose we multiply two $n\times n$ matrices $X$ and $Y$, each consisting of one-digit (base-10) entries, by directly applying this formula. Assume that multiplying two one-digit numbers takes $\Theta(1)$ time, while adding $m$ one- or two-digit numbers together takes $\Theta (m \log m)$ time. In terms of $n$, what is the asymptotic (big-$\Theta$) running time of this algorithm? Explain your answer.\hfill\textbf{(20 points)}

\sol{
We analyze the standard matrix-multiplication algorithm in the worst-case, counting primitive operations as a function \(f(n)\) of the input size \(n\).  As per class content, constants and lower-order terms are ignored in asymptotic notation.

\[
  z_{i,j} \;=\; \sum_{k=1}^{n} x_{i,k}\,y_{k,j},
\]
so every output entry \(z_{i,j}\) requires the same two phases:

\begin{itemize}
  \item \textbf{Multiplications.}  There are \(n\) products \(x_{i,k}y_{k,j}\).  Each one-digit-by-one-digit multiplication is a single primitive step, so this phase costs \(\Theta(n)\).
  \item \textbf{Additions.}  The \(n\) one or two-digit partial products are summed.  Adding \(m\) such numbers takes \(\Theta(m\log m)\); here \(m=n\), giving \(\Theta(n\log n)\).
\end{itemize}

The time for a single entry is therefore
\[
  \Theta(n) + \Theta(n\log n)\;=\;\Theta(n\log n),
\]
because \(n = O(n\log n)\).

There are \(n^2\) entries in the product matrix, and they are computed independently.  Multiplying the per-entry cost by the number of entries gives the total running time:
\[
  n^{2} \times \Theta(n\log n)
  \;=\;
  \boxed{\Theta\!\bigl(n^{3}\log n\bigr)}.
\]
}
\end{enumerate}

\end{document}
