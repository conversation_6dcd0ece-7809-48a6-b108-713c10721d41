\documentclass[twoside,11pt]{article}

%================================ PREAMBLE ==================================

%--------- Packages -----------
\usepackage{fullpage}
\usepackage{amssymb}
\usepackage{amsmath}
\usepackage{amsthm}
\usepackage{latexsym}
\usepackage{tikz}
\usepackage{graphicx}
\usepackage{color}
\usepackage{url}
\usepackage{listings}
\usepackage{fancyhdr}
\lstset{ basicstyle=\ttfamily, columns=fullflexible, frame=single, breaklines=true, postbreak=\mbox{\textcolor{red}{$\rightarrow$}\space} }

%----------Spacing-------------
\setlength{\topmargin}{-0.6 in}
\setlength{\headsep}{0.75 in}
\setlength{\parindent}{0 in}
\setlength{\parskip}{0.1 in}
\setlength{\headheight}{13.6pt}

\newcommand{\boxheader}[1]{
    \pagestyle{fancy}
    \thispagestyle{plain}
    \newpage
    \setcounter{page}{1}
    \noindent
    \begin{center}
        \framebox{
            \vbox{
                \vspace{2mm}
                \hbox to 6.28in { {\bf CIT 5960 Online} \hfill {\bf Summer 2025} }
                \vspace{2mm}
                \hbox to 6.28in { \hfill {\Large Module #1} \hfill }
                \vspace{2mm}
                \hbox to 6.28in { Name: NAME HERE Collaborator: COLLABORATOR NAME HERE \hfill }
                \vspace{2mm}
            }
        }
    \end{center}
    \lhead{Name: NAME HERE Collaborator: COLLABORATOR NAME HERE}
}



%---------Commands-----------
\renewcommand{\Pr}[1]{{\text{Pr}\left[ #1 \right]}}
\newcommand{\E}[1]{{\mathbf{E}\left[ #1 \right]}}
\newcommand{\Var}[1]{{\text{Var}\left( #1 \right)}}
\DeclareMathOperator*{\argmax}{arg\,max}
\DeclareMathOperator*{\argmin}{arg\,min}
\allowdisplaybreaks

\newcommand{\hide}[1]{}

%------Solutions Toggle------
\newcommand{\sol}[1]{{\par{}\textit{Solution: } \par{} #1 \par{}}}
% \newcommand{\sol}[1]{}

%=============================== END PREAMBLE ===============================

%============================ BEGIN DOCUMENT ================================

\begin{document}
\boxheader{3--4}

{\bf Important:} For this homework and all subsequent homeworks, whenever you are asked to design an algorithm, your solution should include a \textbf{clear English description} of the algorithm, a \textbf{proof of correctness}, and an \textbf{analysis} of the time complexity of your algorithm. Remember, your audience is a human, not a computer. This means you should not submit actual code (like Python or Java), and if you choose to include any pseudocode, it should be brief, clear, and accompanied by an English explanation of what it is doing. Insufficiently explained pseudocode can lead to TAs struggling to figure out what you intended, which may result in deductions.
\begin{enumerate}

\item Find the best possible big-$O$ expression that you can for the solution to each the following recurrences, and explain why your answers are correct. In each part, you may assume\footnote{This assumption is a standard and often unstated base case when $T$ denotes an algorithm's running time because in any deterministic algorithm that always terminates, a constant-length input must yield a constant running time. Note that we cannot assume constant running time when the input length is a variable.} for every positive constant $c$ that $T(c) = O(1)$.\hspace*{\fill}\textbf{(5 points each)}
	\begin{enumerate}
		\item $T(n)\leq 5T(n/4)+ \sqrt {n}$
		\item $T(n)\leq 4T(n/5)+n$
		\item $T(n)\leq T(n/3) + \log n$
		\item $T(n)\leq T(n-3)+ 10$
	\end{enumerate}

\item Let $A[1..n]$ be an array of $n$ positive integers, sorted in non-decreasing order. Given a positive integer $t$, we want to know if there are two indices $i, j$ such that 
$A[i] \cdot A[j] = t$. Design an $O(n)$-time algorithm to solve this problem.\hspace*{\fill}({\bf 20 points})


\item
Suppose you are given an integer array $A=[a_{i+1},\ldots,a_n,a_1,\ldots,a_i]$, where $a_1<\ldots<a_n$ and $i$ is some unknown integer with $1\leq i<n$. Design an $O(\log n)$-time algorithm that finds $i$. For simplicity, you may assume that $n$ is a power of 2.\hspace*{\fill}({\bf 20 points})

\item
Suppose you are given two arrays of distinct integers, $A[1..n]$ and $B[1..n]$. Each array is sorted in increasing order, and the arrays have no entries in common. Design an $O(\log n)$-time algorithm that finds the overall $n$\textsuperscript{th}-smallest entry among the $2n$ array entries. For simplicity, you may assume that $n$ is a power of 2.\hspace*{\fill}({\bf 20 points}

\item A set of $n$ sumo wrestlers are facing off in the annual tournament. Each wrestler $i \in \{1,\ldots,n\}$ has an associated strength $s_i$ and weight $w_i$, both of which are integers. You may assume all strengths and weights are distinct. We say that wrestler $i$ can \textit{yeet} wrestler $j$ if $s_i > s_j$ and $w_i > w_j$; that is, if $i$ is both stronger and heavier than $j$. A wrestler $j$ is \textit{unyeetable} if no wrestler $i$ can yeet $j$.

Design an $O(n \log n)$-time algorithm to find all the unyeetable wrestlers. For simplicity, you may assume that $n$ is a power of 2.\hspace*{\fill}({\bf 20 points})


\end{enumerate}

\end{document}