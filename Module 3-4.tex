\documentclass[twoside,11pt]{article}

%================================ PREAMBLE ==================================

%--------- Packages -----------
\usepackage{fullpage}
\usepackage{amssymb}
\usepackage{amsmath}
\usepackage{amsthm}
\usepackage{latexsym}
\usepackage{tikz}
\usepackage{graphicx}
\usepackage{color}
\usepackage{url}
\usepackage{listings}
\usepackage{fancyhdr}
\lstset{ basicstyle=\ttfamily, columns=fullflexible, frame=single, breaklines=true, postbreak=\mbox{\textcolor{red}{$\rightarrow$}\space} }

%----------Spacing-------------
\setlength{\topmargin}{-0.6 in}
\setlength{\headsep}{0.75 in}
\setlength{\parindent}{0 in}
\setlength{\parskip}{0.1 in}
\setlength{\headheight}{13.6pt}

\newcommand{\boxheader}[1]{
    \pagestyle{fancy}
    \thispagestyle{plain}
    \newpage
    \setcounter{page}{1}
    \noindent
    \begin{center}
        \framebox{
            \vbox{
                \vspace{2mm}
                \hbox to 6.28in { {\bf CIT 5960 Online} \hfill {\bf Summer 2025} }
                \vspace{2mm}
                \hbox to 6.28in { \hfill {\Large Module #1} \hfill }
                \vspace{2mm}
                \hbox to 6.28in { Name: Sey Kim →→→ Collaborator: Not Applicable \hfill }
                \vspace{2mm}
            }
        }
    \end{center}
    \lhead{Name: Sey Kim Collaborator: Not Applicable}
}



%---------Commands-----------
\renewcommand{\Pr}[1]{{\text{Pr}\left[ #1 \right]}}
\newcommand{\E}[1]{{\mathbf{E}\left[ #1 \right]}}
\newcommand{\Var}[1]{{\text{Var}\left( #1 \right)}}
\DeclareMathOperator*{\argmax}{arg\,max}
\DeclareMathOperator*{\argmin}{arg\,min}
\allowdisplaybreaks

\newcommand{\hide}[1]{}

%------Solutions Toggle------
% \newcommand{\sol}[1]{{\par{}\textit{Solution: } \par{} #1 \par{}}}
\newcommand{\sol}[1]{{\par\noindent\rule{\textwidth}{0.4pt}\par{}\textbf{SOLUTION: } \par{} #1 \par{}}}
% \newcommand{\sol}[1]{}

%=============================== END PREAMBLE ===============================

%============================ BEGIN DOCUMENT ================================

\begin{document}
\boxheader{3--4}

{\bf Important:} For this homework and all subsequent homeworks, whenever you are asked to design an algorithm, your solution should include a \textbf{clear English description} of the algorithm, a \textbf{proof of correctness}, and an \textbf{analysis} of the time complexity of your algorithm. Remember, your audience is a human, not a computer. This means you should not submit actual code (like Python or Java), and if you choose to include any pseudocode, it should be brief, clear, and accompanied by an English explanation of what it is doing. Insufficiently explained pseudocode can lead to TAs struggling to figure out what you intended, which may result in deductions.

\begin{enumerate}

% PROBLEM 1
\item Find the best possible big-$O$ expression that you can for the solution to each the following recurrences, and explain why your answers are correct. In each part, you may assume\footnote{This assumption is a standard and often unstated base case when $T$ denotes an algorithm's running time because in any deterministic algorithm that always terminates, a constant-length input must yield a constant running time. Note that we cannot assume constant running time when the input length is a variable.} for every positive constant $c$ that $T(c) = O(1)$.\hspace*{\fill}\textbf{(5 points each)}
\\ 

\textit{The Master Theorem applies to recurrences of the form 
\[
    T(n) \;\le\; a\,T\!\bigl(n/b\bigr) \;+\; n^c,
\]
and gives three cases (from lecture and class resources):}
\begin{itemize}
  \item If \(a < b^c\), then \(T(n) = \Theta(n^c)\).
  \item If \(a = b^c\), then \(T(n) = \Theta\bigl(n^c \log n\bigr)\).
  \item If \(a > b^c\), then \(T(n) = \Theta\bigl(n^{\log_b a}\bigr)\).
\end{itemize}

\begin{enumerate}
    \item $T(n)\le 5T(n/4)+\sqrt{n}$
    \sol{
        \textbf{Master Theorem parameters:} $a=5$, $b=4$, $f(n)=n^{1/2}$.  Since 
        \[
            \log_{4}5 \approx 1.161 \;>\; \tfrac12,
        \]
        we are in Master case 3.  Therefore
        \[
            \boxed{T(n) \;=\; \Theta\bigl(n^{\log_{4}5}\bigr)}.
        \]
    }

    \item $T(n)\le 4T(n/5)+n$
    \sol{
        Here $a=4$, $b=5$, $f(n)=n=n^1$.  Since
        \[
            \log_{5}4 \approx 0.861 \;<\; 1,
        \]
        we are in Master case 1. Hence
        \[
            \boxed{T(n)=\Theta(n)}
        \]
    }

\item $T(n)\le T(n/3)+\log n$
\sol{
    Expanding the recurrence $k$ levels yields
    \[
        T(n)\;\le\;T\!\bigl(n/3^{\,k}\bigr)\;+\;
        \sum_{j=0}^{k-1}\log\!\bigl(n/3^{\,j}\bigr)
        \;=\;T\!\bigl(n/3^{\,k}\bigr)\;+\;
        \sum_{j=0}^{k-1}\bigl(\log n - j\log 3\bigr).
    \]
    The recursion stops once $n/3^{\,k}\!\le 1$, i.e.\ 
    $k=\lfloor\log_{3} n\rfloor=\Theta(\log n)$.  
    Ignoring the base term $T(1)=O(1)$ and using the
    arithmetic-series formula,
    \[
        \sum_{j=0}^{k-1}(\log n - j\log 3)
        \;=\;k\log n - \log 3\cdot\frac{(k-1)k}{2}
        \;=\;\Theta\!\bigl((\log n)^2\bigr).
    \]
    Therefore
    \[
        \boxed{T(n)=\Theta\bigl(\log^{2} n\bigr)}.
    \]
}

    \item $T(n)\le T(n-3)+10$
    \sol{
        Each recursive step reduces $n$ by 3 and adds a constant cost of 10.  After about $n/3$ steps, the argument drops to some constant $c_0\le3$, where $T(c_0)=O(1)$.  Therefore,
        \[
            T(n)\;\le\;T(c_0)\;+\;10\cdot\frac{n}{3}
            \;=\;O(1)\;+\;O(n)
            \;=\;O(n).
        \]
        Hence
        \[
            \boxed{T(n)=\Theta(n)}.
        \]
    }
\end{enumerate}

% PROBLEM 2
\item Let $A[1..n]$ be an array of $n$ positive integers, sorted in non-decreasing order. Given a positive integer $t$, we want to know if there are two indices $i, j$ such that 
$A[i] \cdot A[j] = t$. Design an $O(n)$-time algorithm to solve this problem.\hspace*{\fill}({\bf 20 points})
\sol{
    \textbf{Precondition:} All entries are positive, so the product $A[\textit{left}]\cdot A[\textit{right}]$ strictly increases if we increment $\textit{left}$ and strictly decreases if we decrement $\textit{right}$.  

    \textbf{Algorithm Description:}
    Use two pointers $\textit{left} = 1$ and $\textit{right} = n$. While $\textit{left} < \textit{right}$, compute $\textit{product} = A[\textit{left}] \cdot A[\textit{right}]$. If $\textit{product} = t$, return true immediately. If $\textit{product} < t$, increment $\textit{left}$ to increase the product. Otherwise (if $\textit{product} > t$), decrement $\textit{right}$ to decrease the product. Continue until $\textit{left} \ge \textit{right}$, and return false if no valid pair is found.

        \textbf{Proof of Correctness:}
        Suppose there exists a pair $(i^*, j^*)$ with $i^*<j^*$ such that $A[i^*]\cdot A[j^*]=t$. We maintain the invariant that if a valid pair exists, both indices always lie between $\textit{left}$ and $\textit{right}$. Initially, $\textit{left}=1$ and $\textit{right}=n$, so the invariant holds. At each step:
        \begin{itemize}
            \item If $A[\textit{left}]\cdot A[\textit{right}]<t$, then for any index $k\le\textit{right}$, $A[k]\ge A[\textit{left}]$ implies $A[k]\cdot A[\textit{right}]\ge A[\textit{left}]\cdot A[\textit{right}]<t$, so $\textit{left}$ cannot be part of a valid pair and we safely increment $\textit{left}$.
            \item If $A[\textit{left}]\cdot A[\textit{right}]>t$, then for any index $k\ge\textit{left}$, $A[k]\le A[\textit{right}]$ implies $A[\textit{left}]\cdot A[k]\le A[\textit{left}]\cdot A[\textit{right}]>t$, so $\textit{right}$ cannot be part of a valid pair and we safely decrement $\textit{right}$.
        \end{itemize}
        This preserves the invariant until either the valid pair is found or $\textit{left}$ meets $\textit{right}$, proving correctness.

    \textbf{Time Complexity Analysis:}
    Each comparison moves one pointer by at least one position. Since the pointers start $n$ positions apart and move inward, there are at most $n-1$ iterations. Each iteration performs $O(1)$ work, so the total runtime is $O(n)$.
}


% PROBLEM 3
\item
Suppose you are given an integer array $A=[a_{i+1},\ldots,a_n,a_1,\ldots,a_i]$, where $a_1<\ldots<a_n$ and $i$ is some unknown integer with $1\leq i<n$. Design an $O(\log n)$-time algorithm that finds $i$. For simplicity, you may assume that $n$ is a power of 2.\hspace*{\fill}({\bf 20 points})
\sol{

        \textbf{Algorithm Description:} 
        We use binary search to locate the minimum element $a_1$, whose position will determine $i$. Initialize $left = 0$ and $right = n-1$. While $left < right$, compute $mid = \lfloor(left + right)/2\rfloor$. Compare $A[mid]$ with $A[right]$: if $A[mid] > A[right]$, the rotation point (and minimum) must be in the right half, so set $left = mid + 1$. Otherwise, the minimum is at position $mid$ or in the left half, so set $right = mid$. When the loop terminates, $left$ points to the minimum element $a_1$. Since there are $n-i$ elements before $a_1$ in the rotated array, we have $left = n-i$, so return $i = n - left$.

        \textbf{Proof of Correctness:} 
    The key insight is that the rotated array has two strictly increasing portions: the \textit{high} portion $\{a_{i+1}, \ldots, a_n\}$ followed by the \textit{low} portion $\{a_1, \ldots, a_i\}$. The minimum element $a_1$ marks the boundary between these portions. Our invariant is that the minimum element lies within $[left, right]$. When $A[mid] > A[right]$, we know $mid$ is in the \textit{high} portion (since all elements there are greater than those in the \textit{low} portion), so the minimum must be to the right of $mid$. When $A[mid] \leq A[right]$, either $mid$ is the minimum or the minimum is to its left. This correctly narrows the search space while preserving the invariant. Upon termination, $left$ points to $a_1$, and since the \textit{high} portion has exactly $n-i$ elements, we correctly compute $i = n - left$.

    \textbf{Time Complexity Analysis:} 
    This is a standard binary search where we halve the search space in each iteration. We start with a range of size $n$ and reduce it by half until the range has size 1. This takes $\log_2 n$ iterations, and each iteration performs a constant amount of work, so the total time complexity is $O(\log n)$.
}


% PROBLEM 4
\item
Suppose you are given two arrays of distinct integers, $A[1..n]$ and $B[1..n]$. Each array is sorted in increasing order, and the arrays have no entries in common. Design an $O(\log n)$-time algorithm that finds the overall $n$\textsuperscript{th}-smallest entry among the $2n$ array entries. For simplicity, you may assume that $n$ is a power of 2.\hspace*{\fill}({\bf 20 points})
\sol{
    \textbf{Algorithm Description:} We binary search on the number of elements to take from array $A$ in the first $n$ smallest elements overall. Initialize $left = 0$ and $right = n$. While $left \leq right$: set $countA = \lfloor(left + right)/2\rfloor$ and $countB = n - countA$. Define $L_A$ as the largest element in $A[1 \ldots countA]$ (or $-\infty$ if $countA = 0$), $R_A$ as the smallest element in $A[countA+1 \ldots n]$ (or $+\infty$ if $countA = n$), and similarly $L_B, R_B$ for array $B$. If both $L_A \leq R_B$ and $L_B \leq R_A$, we have found the correct partition and return $\max(L_A, L_B)$. If $L_A > R_B$, we are taking too many elements from $A$, so set $right = countA - 1$. Otherwise, set $left = countA + 1$.

    \textbf{Proof of Correctness:} We seek to partition the combined arrays such that the first $n$ elements consist of $A[1 \ldots \textit{countA}]$ and $B[1 \ldots \textit{countB}]$. For this to be a valid partition of the $n$ smallest elements, we need every element in our partition to be $\le$ every element not in our partition. Since arrays are sorted and disjoint, this reduces to checking $L_A \le R_B$ and $L_B \le R_A$. When both conditions hold, we have correctly identified the $n$ smallest elements, and the $n$-th smallest is the maximum of $L_A$ and $L_B$. When $L_A > R_B$, we are including too many large elements from $A$, so we reduce $\textit{countA}$. When $L_B > R_A$, we need more elements from $A$, so we increase $\textit{countA}$.

    \textbf{Time Complexity Analysis:} We perform binary search over the range $[0, n]$, which takes $O(\log n)$ iterations. Each iteration involves constant-time operations, so the total time complexity is $O(\log n)$.
}

% PROBLEM 5
\item A set of $n$ sumo wrestlers are facing off in the annual tournament. Each wrestler $i \in \{1,\ldots,n\}$ has an associated strength $s_i$ and weight $w_i$, both of which are integers. You may assume all strengths and weights are distinct. We say that wrestler $i$ can \textit{yeet} wrestler $j$ if $s_i > s_j$ and $w_i > w_j$; that is, if $i$ is both stronger and heavier than $j$. A wrestler $j$ is \textit{unyeetable} if no wrestler $i$ can yeet $j$.

Design an $O(n \log n)$-time algorithm to find all the unyeetable wrestlers. For simplicity, you may assume that $n$ is a power of 2.\hspace*{\fill}({\bf 20 points})
\sol{
\textbf{Algorithm.}
Insert the codewords into a binary trie and watch for two conflict patterns.

\begin{enumerate}
  \item Create an empty root node (no children, not a leaf).
  \item For each ordered pair $(x,\gamma(x))$ in $A$:
        \begin{enumerate}
          \item Let $w=\gamma(x)$; start at the root.
          \item For each bit $b$ in $w$ (left to right):
                \begin{enumerate}
                  \item \emph{Prefix-hit test:}  
                        If the current node is already marked \texttt{leaf}, \textbf{reject} (an earlier codeword is a prefix of $w$).
                  \item Follow (or create) the child edge labelled $b$.
                \end{enumerate}
          \item After reading the last bit we are at node $v$.
                \begin{enumerate}
                  \item \emph{Extended-prefix test:}  
                        If $v$ currently has a child edge, \textbf{reject} ($w$ is a prefix of a previously inserted codeword).
                  \item Mark $v$ as \texttt{leaf}.
                \end{enumerate}
        \end{enumerate}
  \item When all insertions finish without rejection, \textbf{accept} (the code is prefix‐free).
\end{enumerate}

\textbf{Proof of Correctness.}
We show that the algorithm accepts exactly the prefix codes.

\emph{Soundness.}  
A rejection occurs only if a codeword's insertion meets
(i) a \texttt{leaf} before processing all its bits (some earlier codeword is a prefix of the current one), or
(ii) finishes at a node that already has children (the current codeword is a prefix of an earlier, longer one).
Either situation violates the prefix-free property, so every rejected input is not a prefix code.

\emph{Completeness.}  
Suppose the algorithm accepts.  
Then during every insertion no node encountered prematurely marked \texttt{leaf} and the final node gained no children beforehand.  
Hence for any two distinct codewords $u,w$ their trie paths diverge strictly before the end of either one, so neither is a prefix of the other.  
Therefore the accepted code is prefix-free.

\textbf{Complexity Analysis.}
Each bit of every codeword causes one constant-time trie operation (follow, create, or test).  
Thus total running time is $O(L)$ and the trie uses $O(L)$ space.}


\end{enumerate}

\end{document}