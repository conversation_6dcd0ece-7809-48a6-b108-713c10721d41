\documentclass[twoside,11pt]{article}

%================================ PREAMBLE ==================================

%--------- Packages -----------
\usepackage{fullpage}
\usepackage{amssymb}
\usepackage{amsmath}
\usepackage{amsthm}
\usepackage{latexsym}
\usepackage{clrscode3e}
\usepackage{graphicx}
\usepackage{color}
\usepackage{url}
\usepackage{tikz}
\usepackage{listings}
\usepackage{fancyhdr}
\lstset{ basicstyle=\ttfamily, columns=fullflexible, frame=single, breaklines=true, postbreak=\mbox{\textcolor{red}{$\rightarrow$}\space} }

%----------Spacing-------------
\setlength{\topmargin}{-0.6 in}
\setlength{\headsep}{0.75 in}
\setlength{\parindent}{0 in}
\setlength{\parskip}{0.1 in}
\setlength{\headheight}{13.6pt}

\newcommand{\boxheader}[1]{
    \pagestyle{myheadings}
    \thispagestyle{plain}
    \newpage
    \setcounter{page}{1}
    \noindent
    \begin{center}
        \framebox{
            \vbox{
                \vspace{2mm}
                \hbox to 6.28in { {\bf CIT 596 Online} \hfill {\bf Summer 2025} }
                \vspace{2mm}
                \hbox to 6.28in { \hfill {\Large Module #1 Assignment} \hfill }
                \vspace{2mm}
                \hbox to 6.28in { Name: Sey Kim (No collaborator)  \hfill }

                \vspace{2mm}
            }
        }
    \end{center}
}



%---------Commands-----------
\renewcommand{\Pr}[1]{{\text{Pr}\left[ #1 \right]}}
\newcommand{\E}[1]{{\mathbf{E}\left[ #1 \right]}}
\newcommand{\Var}[1]{{\text{Var}\left( #1 \right)}}
\DeclareMathOperator*{\argmax}{arg\,max}
\DeclareMathOperator*{\argmin}{arg\,min}
\allowdisplaybreaks

%------Solutions Toggle------
\newcommand{\sol}[1]{{\par{}\textit{Solution: } \par{} #1 \par{}}}
% \newcommand{\sol}[1]{}

%=============================== END PREAMBLE ===============================

%============================ BEGIN DOCUMENT ================================

\begin{document}
\boxheader{5--6}

{\bf Important:} For this homework and all subsequent homeworks, whenever you are asked to design an algorithm, your solution should include a \textbf{clear English description} of the algorithm, a \textbf{proof of correctness}, and an \textbf{analysis} of the time complexity of your algorithm. Remember, your audience is a human, not a computer. This means you should not submit actual code (like Python or Java), and if you choose to include any pseudocode, it should be brief, clear, and accompanied by an English explanation of what it is doing. Insufficiently explained pseudocode can lead to TAs struggling to figure out what you intended, which may result in deductions.

\begin{enumerate}

	\item You are given the adjacency-list representation of a directed acyclic graph $G=(V,E)$ with $n$ vertices and $m$ edges. Furthermore, for every vertex $v\in V$ with in-degree zero, you are given a number $value(v)$. For every vertex $v\in V$ with positive in-degree, we define
	\[value(v)=\sum_{(u,v)\in E} value(u),\]
	the sum of the values of all of $v$'s predecessors in the graph.
	
	For example, in the graph below, the gray vertices have in-degree zero, so their values determine the values of all other vertices in the graph.
	
	\[\begin{tikzpicture}[scale=0.75]
			\tikzstyle{every node}+=[draw,thick,circle,inner sep=0pt,minimum size=20]
			\node[fill=lightgray] (a) at (0,0)  {$3$};
			\node (b) at (3,0)  {$6$};
			\node (c) at (6,0)  {$13$};
			\node[fill=lightgray] (d) at (9,0) {$5$};
			\node (e) at (12,0)  {$7$};
			\node (f) at (0,3)  {$3$};
			\node (g) at (3,3) {$3$};
			\node (h) at (6,3) {$3$};
			\node (i) at (9,3) {$10$};
			\node[fill=lightgray] (j) at (12,3)  {$2$};
			\tikzstyle{every edge}+=[->,thick]
			\draw (g) edge (h);
			\draw (h) edge (c);
			\draw (d) edge (i);
			\draw (j) edge (i);
			\draw (f) edge (g);
			\draw (h) edge (i);
			\draw (g) edge (b);
			\draw (a) edge (b) edge (f);
			\draw (d) edge (e);
			\draw (j) edge (e);
			\draw (i) edge (c);
			\end{tikzpicture}\]
			
	Design an $O(n+m)$-time algorithm to compute $value(v)$ for all vertices $v\in V$.~ \hfill{({\bf 20 points})}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Question 1 Answer
\sol {
		\textbf{Algorithm Description.}  
		Process the DAG in topological order, propagating each vertex's value to its
		out-neighbors. A way to generate a topological order in linear
		time is Kahn's BFS-style algorithm that uses the in-degree of every vertex.
		
		\begin{enumerate}
			\item For every vertex $v$ compute its in-degree $\textit{indeg}(v)$
			      (already available from the adjacency lists) and initialize an array
			      $\textit{val}[v]$ with the given $value(v)$ for sources and $0$ for all
			      other vertices.
			\item Put every source , the vertex with $\textit{indeg}(v)=0$, into a queue $Q$.
			\item While $Q$ is not empty, dequeue a vertex $u$ and \emph{push} its
			      value forward: for every out-neighbor $w$ of $u$
			      \begin{enumerate}
			      	\item add $\textit{val}[u]$ to $\textit{val}[w]$;
			      	\item decrement $\textit{indeg}(w)$; if it becomes $0$, enqueue $w$.
			      \end{enumerate}
			\item When the queue empties, $\textit{val}[v]$ equals $value(v)$ for all
			      $v\in V$.
		\end{enumerate}
		
		\vspace{0.5em}
		\textbf{Proof of Correctness.}  
		The queue processing order produced by Kahn’s algorithm is a valid
		topological order of $G$.  
		We prove by induction on this order that after a vertex $v$ is
		dequeued, $\textit{val}[v]=value(v)$.
		
		\emph{Base step.}  Any source $s$ is dequeued first; its
		given value is already correct.
		
		\emph{Induction step.}  Assume every vertex dequeued earlier than
		$v$ has its value correct and observe that all predecessors of $v$ appear
		earlier in the topological order.  When the last predecessor $u$ of $v$ is
		processed, it adds $\textit{val}[u]=value(u)$ to $\textit{val}[v]$ and
		reduces $\textit{indeg}(v)$ to zero, so $v$ is enqueued with
		\[
		    \textit{val}[v]=\sum_{(x,v)\in E} value(x)=value(v).
		\]
		Thus the invariant holds, and by induction every vertex leaves the queue
		with its correct value.  Consequently, upon termination the algorithm has
		computed $value(v)$ for every $v\in V$.
		
		\vspace{0.5em}
		\textbf{Time‑Complexity Analysis.}  
		Each vertex is enqueued and dequeued exactly once, and each edge is
		examined exactly once when its tail vertex is processed.  All other
		operations are $O(1)$.  Therefore the total running time is
		\[
		    O(|V|+|E|)=O(n+m).
		\]
		Since every vertex and edge must be processed at least once, this bound is tight, so the time complexity is $\Theta(n+m)$.
	}
	
    \item Suppose you are given the adjacency-list representation of a directed graph $G=(V,E)$ with $n$ vertices and $m$ edges, and you want to find the minimum number of directed edges that could be added to $G$ to make it strongly connected. For example, the DAG in Q1 can be made strongly connected by the addition of three directed edges. Design an $O(n+m)$-time algorithm that finds this number. ~\hfill{({\bf 20 points})} 

	%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Question 2 Answer
	\sol{
	\textbf{Algorithm.}  
	(1) Run Tarjan's (or Kosaraju's) algorithm to find all strongly-connected components (SCCs) of $G$; let $k$ be their number and label each vertex with its component ID (time $O(n+m)$).  
	(2) For each edge $(u,v)\in E$, if $\textit{id}(u)\neq\textit{id}(v)$, increment $\textit{outDeg}[\textit{id}(u)]$ and $\textit{inDeg}[\textit{id}(v)]$ (another $O(n+m)$ pass).  
	(3) Let  
	\[
	s=\bigl|\{c:\textit{inDeg}[c]=0\}\bigr|\quad\text{and}\quad
	t=\bigl|\{c:\textit{outDeg}[c]=0\}\bigr|.
	\]
	If $k=1$ return $0$ (already strongly connected); otherwise return $\max(s,t)$.

	\textbf{Proof of Correctness.}  
	Let $D$ be the condensation DAG whose vertices are the SCCs.  
	• \emph{Lower bound:} A single new edge can reduce the number of sources by at most 1 and the number of sinks by at most 1; hence at least $\max(s,t)$ edges are necessary.  
	• \emph{Upper bound:} List the sources $p_1,\dots,p_s$ and sinks $q_1,\dots,q_t$, padding the shorter list by repeats so both have length $r=\max(s,t)$.  
	Add the edges $(q_1\to p_1),\dots,(q_r\to p_r)$.  
	These $r$ edges form a directed cycle through every component, giving a strongly connected graph.  
	Thus $\max(s,t)$ edges are both necessary and sufficient.

	\textbf{Time Complexity.}  
	Tarjan/Kosaraju: $O(n+m)$.  
	Single extra pass over all edges: $O(n+m)$.  
	Counting sources and sinks: $O(k)\le O(n)$.  
	Total: $O(n+m)$. Since we must examine every vertex and edge, this bound is tight, giving $\Theta(n+m)$.
	}

    \item The \textbf{diameter} of a graph is defined as the maximum, over all pairs $(u,v)$ of vertices, of the distance from $u$ to $v$ in that graph. Design an algorithm that, given the adjacency-list representation of any undirected tree graph $T$ with $n$ vertices and $m$ edges, finds the diameter of $T$ in $O(n)$ time. ~\hfill{({\bf 20 points})} 

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Question 3 Answer
	\sol{
\textbf{Algorithm.}  
\begin{enumerate}
  \item Pick an arbitrary vertex $s$ and run a breadth-first search (BFS) to
        find the vertex $u$ farthest from $s$.
  \item Run a second BFS starting from $u$; let $d$ be the maximum distance
        discovered and $v$ the vertex at that distance.
  \item Output $d$ (the length of path $u\!\to\!v$).  The vertices $u$ and $v$
        themselves can be reported by storing parents during the second BFS
        (not required for the distance value).
\end{enumerate}

\textbf{Proof of Correctness.}  
Let $P=x_0,x_1,\dots,x_k$ be any longest path in $T$ ($k$ is the diameter).
Because a tree is acyclic, $P$ is unique once its endpoints $x_0,x_k$ are
chosen.  
\begin{itemize}
  \item The first BFS returns a vertex $u$ that is farthest from $s$.
        In every tree, at least one endpoint of \emph{some} longest path is
        farthest from any chosen start vertex, so $u$ equals $x_0$ or $x_k$ for
        some longest path $P$.
  \item The second BFS starts at this endpoint $u$.
        In a tree, the farthest vertex from one endpoint of a longest path is
        the other endpoint of the same path.  
        Hence the vertex $v$ found in the second BFS is $x_k$ (or $x_0$), and
        the distance $d$ equals $k$.
\end{itemize}
Therefore the algorithm outputs the diameter.

\textbf{Time Complexity.}  
Each BFS scans every vertex and edge once; for a tree $m=n-1$, so each BFS
runs in $O(n)$ time.  Two BFS traversals yield a total running time
$O(n)$. Since we must visit every vertex at least once, this bound is tight, giving $\Theta(n)$.
}


    \item You are driving an electric car from New York to San Francisco along Interstate 80, and you know the locations of the charging stations along the way. You are given the locations of the charging stations as an array, sorted in increasing order, of distances from New York. After a charge, your car has a range of $R$ miles.  Assume that for any charging station, the next one is within distance $R$; so you will be able to complete your journey. Design an $O(n)$-time algorithm that finds the minimum number of charging stations you must stop at.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Question 4 Answer
	\sol{

\textbf{Algorithm.}  
Append the destination as a sentinel station
$p[n+1] = +\infty$ (any value $> p[n]+R$ works).  
Set $\textit{stops}=0$, $\textit{last}=0$ (last charge location, starting in New York at position 0).  
Scan the stations once:

\begin{enumerate}
\item For $i=1$ to $n+1$:
      \begin{itemize}
        \item If $p[i]-\textit{last} > R$ \quad// can't reach $p[i]$ on
              current charge
              \begin{itemize}
                \item \textbf{Stop} at $p[i-1]$  
                      $\rightarrow$ set $\textit{last}=p[i-1]$,
                      increment $\textit{stops}$.
              \end{itemize}
        \item Continue scanning.
      \end{itemize}
\end{enumerate}

When the loop finishes (upon reading the sentinel), $\textit{stops}$ is the
minimum number of charging stops.

\textbf{Proof of Correctness.}  
Process stations in order; whenever the next station is out of range, we stop
at the \emph{furthest} station still reachable ($p[i-1]$).  
Let the sequence of stops chosen by the algorithm be
$S=s_1,s_2,\dots ,s_k$.

\emph{Claim 1 (feasibility).}  
By construction $p[i]-\textit{last}\le R$ holds just after each stop, so every
leg between consecutive stops (and the final leg to the destination) is at
most $R$ miles.  Hence the algorithm completes the trip.

\emph{Claim 2 (optimality).}  
Consider the first index where the algorithm and any optimal solution $O$
differ.  The algorithm's stop $s_j$ is the farthest station reachable on the
current charge; therefore $O$ cannot stop \emph{after} $s_j$, only at $s_j$ or
before.  Replacing $O$'s earlier (or equal) stop by $s_j$ keeps all later legs
feasible and never increases the total number of stops.  Repeating this swap
argument for each divergence transforms $O$ into the algorithm's schedule
without increasing the stop count, so the algorithm is optimal.

\textbf{Time Complexity.}  
The scan inspects each of the $n+1$ array positions once and performs $O(1)$
work per position.  Thus the running time is $O(n)$. Since we must examine every charging station, this bound is tight, giving $\Theta(n)$.
}
    
    ~\hfill{({\bf 20 points})}

    \item Suppose you are given a binary code $\gamma$ for an alphabet $\Sigma$ that has $n$ symbols. You are given this as an array $A$ of length $n$, where each entry is an ordered pair $(x,\gamma(x))$, where $x$ is a symbol in the alphabet and $\gamma(x)$ is a binary string, the codeword for $x$. Let $L=\sum_{x\in\Sigma}|\gamma(x)|$ be the sum of all codeword lengths.

    Design an $O(L)$-time algorithm that determines if $\gamma$ is a prefix code.~\hfill{({\bf 20 points})}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Question 5 Answer
	\sol{

\textbf{Algorithm (trie scan, $O(L)$).}
\begin{enumerate}
  \item Create a root node of an empty binary trie.  Each node has two
        child pointers ($0$ and $1$) and a Boolean \textit{terminal} flag,
        all initially $\mathsf{null}$ or \textit{false}.
  \item For each codeword $w=\gamma(x)$ in $A$ do  
        \texttt{insert($w$)}:
        \begin{enumerate}
          \item Start at the root; for each bit $b$ of $w$
                \begin{enumerate}
                  \item \textbf{If} the current node's
                        \textit{terminal} flag is \textit{true},
                        \textbf{reject} (some earlier word is a prefix of $w$).
                  \item \textbf{If} child[$b$] is $\mathsf{null}$,
                        create a new node and link it.
                  \item Move to child[$b$].
                \end{enumerate}
          \item After the last bit:
                \begin{enumerate}
                  \item \textbf{If} \textit{terminal} is already \textit{true},
                        \textbf{reject} (duplicate word).
                  \item \textbf{If} the node has a non-null child,
                        \textbf{reject} ($w$ is a prefix of a word already inserted).
                  \item Otherwise set \textit{terminal} $\leftarrow$ \textit{true}.
                \end{enumerate}
        \end{enumerate}
  \item If all insertions succeed, \textbf{accept} (the code is a prefix code).
\end{enumerate}

\textbf{Proof of Correctness.}
We show that the algorithm accepts \emph{iff} no codeword is a prefix of
another.

\emph{Soundness (no false accepts).}  
During insertion, two situations trigger rejection:

* Encountering a node whose \textit{terminal} flag is already true
  implies an earlier codeword ends at this node, hence is a prefix of
  the current word.
* Completing the current word at a node that already has children
  implies the current word is a prefix of a previously inserted longer
  word.

Thus if the algorithm accepts, neither condition ever occurred, so no
word is a prefix of another; $\gamma$ is a prefix code.

\emph{Completeness (no false rejects).}  
If $\gamma$ is \emph{not} a prefix code, there exists a pair
$u,v$ with $u$ a prefix of $v$.  
Whichever of the two is processed second will trigger one of the
rejection tests above, so the algorithm will reject.  
Hence the algorithm rejects exactly the non-prefix codes.

\textbf{Time-Complexity Analysis.}
Each bit of every codeword is processed once, performing only constant-time
pointer operations.  The total work is therefore $\sum_{x\in\Sigma}|\gamma(x)|
= L$, giving a running time of $O(L)$. Since every bit must be examined, this bound is tight, so the time complexity is $\Theta(L)$.
The trie uses at most $L$ new nodes, so the algorithm also requires
$O(L)$ space.
}

\end{enumerate}     

\end{document}