\documentclass[twoside,11pt]{article}

%================================ PREAMBLE ==================================

%--------- Packages -----------
\usepackage{fullpage}
\usepackage{amssymb}
\usepackage{amsmath}
\usepackage{amsthm}
\usepackage{latexsym}
\usepackage{clrscode3e}
\usepackage{graphicx}
\usepackage{color}
\usepackage{url}
\usepackage{tikz}
\usepackage{listings}
\usepackage{fancyhdr}
\lstset{ basicstyle=\ttfamily, columns=fullflexible, frame=single, breaklines=true, postbreak=\mbox{\textcolor{red}{$\rightarrow$}\space} }

%----------Spacing-------------
\setlength{\topmargin}{-0.6 in}
\setlength{\headsep}{0.75 in}
\setlength{\parindent}{0 in}
\setlength{\parskip}{0.1 in}
\setlength{\headheight}{13.6pt}

\newcommand{\boxheader}[1]{
    \pagestyle{myheadings}
    \thispagestyle{plain}
    \newpage
    \setcounter{page}{1}
    \noindent
    \begin{center}
        \framebox{
            \vbox{
                \vspace{2mm}
                \hbox to 6.28in { {\bf CIT 5960 Online} \hfill {\bf Summer 2025} }
                \vspace{2mm}
                \hbox to 6.28in { \hfill {\Large Module #1 Assignment} \hfill }
                \vspace{2mm}
                \hbox to 6.28in { Name: Sey Kim -- No collaborators  \hfill }

                \vspace{2mm}
            }
        }
    \end{center}
}
\usepackage[normalem]{ulem}



%---------Commands-----------
\renewcommand{\Pr}[1]{{\text{Pr}\left[ #1 \right]}}
\newcommand{\E}[1]{{\mathbf{E}\left[ #1 \right]}}
\newcommand{\Var}[1]{{\text{Var}\left( #1 \right)}}
\DeclareMathOperator*{\argmax}{arg\,max}
\DeclareMathOperator*{\argmin}{arg\,min}
\allowdisplaybreaks

%------Solutions Toggle------
\newcommand{\sol}[1]{{\par{}\noindent\rule{\textwidth}{0.6pt}\vspace{1ex}\par{}\textbf{\textit{Solution}} \par{} #1 \par{}}}
% \newcommand{\sol}[1]{}

%=============================== END PREAMBLE ===============================

%============================ BEGIN DOCUMENT ================================

\begin{document}
\boxheader{7--8}
Answers that describe an algorithm should always include a proof of correctness and running time analysis. Algorithms may be described in English or in clear pseudocode with explanatory comments.

\begin{enumerate}

% Question 1
\item
Prove or disprove (by giving a counterexample) each statement below. 
Assume that the graph $G=(V, E)$ is a \emph{connected} undirected weighted graph such that all edge weights are positive. Do not assume that edge weights are distinct unless this is specifically stated. 

\hfill{({\bf 5 points each})}


\begin{enumerate}
\item If $G$ has more than $|V| - 1$ edges, and there is a unique heaviest edge, then this edge
cannot be part of any MST.


\item
Let $e$ be any edge of minimum weight in $G$. Then $e$ must be part of some MST.


\item If $G$ contains a unique edge $e$ of minimum weight, then $e$ must be part of every MST.

\item If $e$ is part of some MST of $G$, then it must be a minimum weight edge across some cut of $G$.

\item 
For every pair of vertices in $G$, a shortest path between them is necessarily part of some MST.

\end{enumerate}

\sol{
\begin{enumerate}
  \item[\textbf{(a)}] \textbf{False.}  Take two disjoint triangles whose internal edges all have weight \(1\), joined by a single bridge of weight \(10\).  
        That unique heaviest edge must appear in every MST, so the claim fails.

  \item[\textbf{(b)}] \textbf{True.}  Any minimum-weight edge can be processed first in Kruskal’s algorithm (no cycle is formed at that point), so some MST contains it.

  \item[\textbf{(c)}] \textbf{True.}  A unique lightest edge crosses its own cut; by the cut property it must lie in every MST.

  \item[\textbf{(d)}] \textbf{True.}  If an edge \(e\) lies in an MST, removing it defines a cut.  
        Any lighter edge across that cut would yield a cheaper tree, contradicting optimality; hence \(e\) is a lightest crossing edge of that cut.

  \item[\textbf{(e)}] \textbf{False.}  In the triangle with edge weights \(w(ab)=4\), \(w(ac)=3\), \(w(bc)=3\),  
        the unique shortest path between \(a\) and \(b\) is the single edge \(ab\).  
        However, \(ab\) is the unique heaviest edge of this cycle, so by the cycle property every MST is \(\{ac,bc\}\), omitting \(ab\).
\end{enumerate}
}

% 
% 
% 
% 
% 
% 
% Question 2
\newpage
\item Suppose a weighted graph $G$ has at least two different minimum spanning trees. (Two spanning trees are considered different if there is at least one edge in one tree that does not occur in the other.) Prove that in any cycle formed by the union of the edges in the two trees, the maximum weight edge is not unique.
\hfill{({\bf 25 points})}


\sol{
Let $T_1$ and $T_2$ be two distinct Minimum Spanning Trees (MSTs) of a weighted graph $G$. Since $T_1$ and $T_2$ are distinct, their edge sets are not identical. Let $C$ be any cycle in the graph formed by the union of their edges, $T_1 \cup T_2$.

We will prove by contradiction that the maximum-weight edge in $C$ cannot be unique. Assume for contradiction that there is a unique maximum-weight edge in $C$, and let this edge be $e$.

We consider two cases based on where $e$ lies.

\textbf{\uwave{Case 1: $e$ is in one tree but not the other.}}
Without loss of generality, assume $e \in T_1$ and $e \notin T_2$.
Now, consider the tree $T_2$. Since $e \notin T_2$, adding the edge $e$ to $T_2$ must create a cycle. This cycle is precisely $C$, as it consists of $e$ and the unique path in $T_2$ between the endpoints of $e$.

By the cycle property from lecture, for any cycle in a graph, no edge in an MST can be strictly heavier than all other edges in that cycle. So for the MST $T_2$ and the cycle $C$, the edge $e$ cannot be strictly heavier than all other edges of $C$ (which are all in $T_2$). There must be some other edge $f \in C$ such that $w(f) \ge w(e)$.
But this contradicts our assumption that $e$ is the \emph{unique} maximum-weight edge in $C$ (which means $w(f) < w(e)$ for all $f \neq e$).

\textbf{\uwave{Case 2: $e$ is in both trees.}}
Assume $e \in T_1 \cap T_2$.
Consider the tree $T_1$. Removing the edge $e$ from $T_1$ splits the vertices of the graph into two disjoint sets, say $U$ and $V \setminus U$. This defines a cut $(U, V \setminus U)$.
The cycle $C$ must cross this cut at least twice. One edge crossing the cut is $e$. The rest of the cycle, $C \setminus \{e\}$, forms a path that also connects a vertex in $U$ to a vertex in $V \setminus U$. Therefore, there must be at least one other edge in $C$, say $f'$, that also crosses the cut $(U, V \setminus U)$.

By the cut property from lecture, for any cut in the graph, any edge in an MST that crosses the cut must be a minimum-weight edge among all edges crossing that cut.
Using this property for the MST $T_1$ and the cut $(U, V \setminus U)$, we have that $w(e) \le w(f')$ for any edge $f'$ crossing the cut.
But our assumption was that $e$ is the \emph{unique} maximum-weight edge in the cycle $C$. This means for any other edge in $C$, including $f'$, we must have $w(f') < w(e)$.
We get $w(e) \le w(f')$ and $w(f') < w(e)$, which is a contradiction.

Since both cases lead to a contradiction, our assumption must be false. Therefore, in any cycle formed by the union of two different MSTs, the maximum-weight edge is not unique.
}


% 
% 
% 
% 
% 
% 
% 
% Question 3

\newpage

\item A soccer stadium is hosting a match between two rival teams with unruly fan bases, so they need to put up a barrier in the stands to separate opposing hooligans. This barrier will occupy some seats, and the goal is to minimize the resulting reduction in ticket revenue.

	The seats are arranged in a grid with $m$ rows and $n$ columns. The completed barrier will extend from row 1 to row $m$, and it will occupy a pair of adjacent seats in each row. There is no restriction on where in row 1 the barrier starts, and it is allowed to zig-zag left and right, but it can only shift by one seat per row.

	That is, if the barrier occupies seats $(i,j)$ and $(i,j+1)$ in row $i$, then the seats it occupies in row $i+1$ must be either $(i+1,j-1)$ and $(i+1,j)$; $(i+1,j)$ and $(i+1,j+1)$; or $(i+1,j+1)$ and $(i+1,j+2)$.

	(There is no requirement about balancing the number of seats on each side of the barrier. For example, placing the entire barrier in the rightmost two columns is allowed.)

	Here is an example of a valid barrier with $m=8$ and $n=12$:

	\begin{center}
	\begin{tikzpicture}
			\draw[step=0.5cm] (-3,-2) grid (3,2);
			\fill (-1.5,-2) -- (-1.5,-1.5) -- (-1,-1.5) -- (-1,-0.5) -- (-0.5,-0.5) -- (-0.5,0) -- (-1,0) -- (-1,1) -- (-0.5,1) -- (-0.5,1.5) -- (0,1.5) -- (0,2) -- (1,2) -- (1,1.5) -- (0.5,1.5) -- (0.5,1) -- (0,1) -- (0,0) -- (0.5,0) -- (0.5,-0.5) -- (0,-0.5) -- (0,-1.5) -- (-0.5,-1.5) -- (-0.5,-2);
	\end{tikzpicture}
	\end{center}
	
	Each seat $(i,j)$ has a positive price $P(i,j)$, which you can access in $O(1)$ time. The \emph{cost} of a complete barrier is the sum of the prices of the $2m$ seats it occupies. Design a dynamic programming algorithm that computes the minimum possible cost of a complete barrier and runs in $O(mn)$ time. \hfill{({\bf 25 points})}\\

    
\sol{
If we work row by row, keeping a table \(dp[i,j]\) for
\(1\!\le\! i\!\le\! m\) and \(1\!\le\! j\!\le\! n{-}1\).
The entry \(dp[i,j]\) stores the minimum total ticket value lost
by a barrier that spans rows \(1\) through \(i\)
and currently occupies the adjacent seats \((i,j)\) and \((i,j{+}1)\).

\textbf{Algorithm}
\begin{itemize}
\item \emph{Initialize first row:}\\
      \(\displaystyle dp[1,j]\gets P(1,j)+P(1,j+1)\) for every \(j\).
\item \emph{Fill subsequent rows:}\\
      for \(i=2\) to \(m\)  
      \qquad for \(j=1\) to \(n-1\)  
      \qquad\qquad
      \(dp[i,j]\gets P(i,j)+P(i,j+1)+
      \min\{dp[i-1,j-1],dp[i-1,j],dp[i-1,j+1]\}\),
      ignoring any option that falls outside \(1..n-1\).
\item \emph{Answer:} \(\min_{1\le j\le n-1} dp[m,j]\).
\end{itemize}

\textbf{Proof of correctness:}
The table is built top-down.  
For row \(1\) each entry is just the cost of taking those two seats,
so \(dp[1,j]\) is clearly optimal.
Assume every entry in row \(i{-}1\) is optimal.
Any legal extension of a barrier to row \(i\) ending in
\((i,j),(i,j{+}1)\) must come from exactly one of the three
valid column pairs above it.
The recurrence checks every possible option
and adds the fixed cost of the two new seats,
picking the minimum.
So \(dp[i,j]\) is optimal for row \(i\).
By induction, every table entry is optimal, and the final minimum
over the last row gives the minimum possible ticket revenue loss.

\textbf{Running-time analysis.}
The table has \(m(n-1)=\Theta(mn)\) entries.
Each entry is filled with a constant amount of work
(three comparisons and two price look-ups),
so the running time is \(O(mn)\).
The space requirement is the same order,
or \(O(n)\) if we retain only the current and previous rows.
}
% 
% 
% 
% 
% 
% 

% Question 4

\newpage

\item This question explores a {\bf dense clustering} problem that arises in compiling the {\bf switch} statement in the C programming language.

The input to this problem is a set $S$ of integers and a positive real number $\rho < 1$.

A \emph{cluster} is a nonempty subset $C\subseteq S$. A \emph{partition} of $S$ is a collection of clusters such that each element of $S$ belongs to exactly one cluster. The \emph{density} of a cluster $C$ is
\[\frac{|C|}{\max(C)-\min(C)+1}.\]
For example, the density of the cluster $\{-4,3,10\}$ is $\frac{3}{10-(-4)+1}=0.2$. A partition is \emph{valid} if the density of each cluster in the collection is greater than or equal to the number $\rho$.

The goal is to find a valid partition that uses as few clusters as possible. Such a partition is called \emph{optimal}. For example, if $S=\{1,4,8,14,15,22,23,24,27,29\}$ and $\rho=0.5$, then an optimal partition is $\{\{1,4\},\{8\},\{14,15\},\{22,23,24,27,29\}\}$. This partition is valid as the four clusters have densities $0.5$, $1$, $1$, and $0.625$, which all meet or exceed the given density requirement $\rho$, and it would be impossible to make a valid partition with only three clusters for this choice of $S$ and $\rho$.

\begin{enumerate}

\item The \emph{range} of a cluster is the interval from its smallest element to its largest element. Prove there is always an optimal partition where no two clusters have overlapping ranges.

\item Design an $O(n^2)$-time dynamic programming algorithm that, given a set $S$ of $n$ integers and a positive density requirement $\rho<1$, finds an optimal partition.

\end{enumerate}
\hfill{({\bf 25 points})}

\sol{
\begin{enumerate}

\item \textbf{Optimal partitions have disjoint ranges.}
Assume for contradiction that an optimal partition $P$ contains two distinct clusters, $C$ and $D$, whose ranges $I_C = [\min(C), \max(C)]$ and $I_D = [\min(D), \max(D)]$ are not disjoint.
Let $U = C \cup D$. Since $C$ and $D$ are disjoint subsets of $S$, $|U| = |C| + |D|$. The range of the combined cluster $U$ is $I_U = I_C \cup I_D$.
Let $L(I) = \max(I) - \min(I) + 1$ denote the number of integers in an interval $I$.
The validity of $C$ and $D$ means $|C| \ge \rho \cdot L(I_C)$ and $|D| \ge \rho \cdot L(I_D)$. Summing these gives:
\[ |U|=|C|+|D| \ge \rho(L(I_C)+L(I_D)) \]
For any two integer intervals, the sum of their lengths is $L(I_C) + L(I_D) = L(I_C \cup I_D) + L(I_C \cap I_D)$. Since $I_C$ and $I_D$ are not disjoint, their intersection is a non-empty interval, so its length $L(I_C \cap I_D) \ge 1$.
Thus, $L(I_C)+L(I_D) \ge L(I_U) + 1$. Substituting this into the inequality for $|U|$:
\[ |U| \ge \rho(L(I_U)+1) \]
The density of $U$ is therefore $\frac{|U|}{L(I_U)} \ge \frac{\rho(L(I_U) + 1)}{L(I_U)} = \rho \left(1 + \frac{1}{L(I_U)}\right) > \rho$.
So, $U$ is a valid cluster. Replacing $C$ and $D$ in the partition $P$ with the single cluster $U$ yields a new valid partition with one fewer cluster. This contradicts the assumption that $P$ is optimal.
Therefore, in any optimal partition, the ranges of any two clusters must be disjoint.

\item \textbf{\(O(n^{2})\) dynamic program.}
From part (a), an optimal partition must have disjoint cluster ranges. If we sort the elements of $S$ as $x_1 < x_2 < \dots < x_n$, this means any cluster in an optimal partition must be a contiguous block of elements $\{x_i, x_{i+1}, \dots, x_j\}$. So we need to partition the sorted sequence into a minimum number of valid segments, which we can solve with dynamic programming.

\begin{enumerate}
\item Sort the elements of \(S\) as \(x_{1}<\dots<x_{n}\).
\item For every \(1\le i\le j\le n\), pre-compute if the cluster $\{x_i, \dots, x_j\}$ is valid. Store this in a boolean table:
      \[\text{valid}[i,j] \leftarrow \left(\frac{j-i+1}{x_{j}-x_{i}+1} \ge \rho\right).\]
      This takes \(O(n^{2})\) time.
\item Let \(dp[j]\) be the minimum number of clusters for a valid partition of $\{x_1, \dots, x_j\}$. Set \(dp[0]=0\). For \(j=1,\dots,n\), compute:
      \[
        dp[j]=\min_{\substack{1\le i\le j \\ \text{valid}[i,j]}}(dp[i-1]+1).
      \]
      Store the minimizing index \(i\) in \(\text{prev}[j]\) to allow reconstruction of the partition.
\item The minimum number of clusters for all of $S$ is \(dp[n]\). Reconstruct the clusters by backtracking from \(j=n\) using the \(\text{prev}\) array.
\end{enumerate}

\textbf{Proof of Correctness:}
The algorithm works because part (a) lets us reduce the problem to optimally partitioning a sequence. The subproblem \(dp[j]\) finds the minimum number of clusters for the prefix \(\{x_{1},\dots,x_{j}\}\). The recurrence tries all possible valid final clusters \(\{x_{i},\dots,x_{j}\}\) and combines them with the optimal solution for the remaining prefix \(\{x_{1},\dots,x_{i-1}\}\). By induction on the optimal substructure, \(dp[n]\) gives the optimal solution for the entire set.

\textbf{Running time Analysis:}
Sorting takes \(O(n\log n)\). Building the \(\text{valid}\) table takes \(O(n^{2})\). Filling the \(dp\) table takes \(O(n^2)\) time, as computing each of the $n$ entries requires a loop of up to $n$ steps. Reconstruction is $O(n)$. Thus the overall complexity is dominated by the DP and pre-computation, resulting in \(O(n^{2})\) time and \(O(n^{2})\) space (for the 'valid table).
\end{enumerate}
}

\end{enumerate}

\end{document}