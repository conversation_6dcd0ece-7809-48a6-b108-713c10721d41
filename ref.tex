\documentclass[twoside,11pt]{article}

%================================ PREAMBLE ==================================

%--------- Packages -----------
\usepackage{fullpage}
\usepackage{amssymb}
\usepackage{amsmath}
\usepackage{amsthm}
\usepackage{latexsym}
\usepackage{clrscode3e}
\usepackage{graphicx}
\usepackage{color}
\usepackage{url}
\usepackage{tikz}
\usepackage{listings}
\usepackage{fancyhdr}
\lstset{ basicstyle=\ttfamily, columns=fullflexible, frame=single, breaklines=true, postbreak=\mbox{\textcolor{red}{$\rightarrow$}\space} }

%----------Spacing-------------
\setlength{\topmargin}{-0.6 in}
\setlength{\headsep}{0.75 in}
\setlength{\parindent}{0 in}
\setlength{\parskip}{0.1 in}
\setlength{\headheight}{13.6pt}

\newcommand{\boxheader}[1]{
    \pagestyle{myheadings}
    \thispagestyle{plain}
    \newpage
    \setcounter{page}{1}
    \noindent
    \begin{center}
        \framebox{
            \vbox{
                \vspace{2mm}
                \hbox to 6.28in { {\bf CIT 596 Online} \hfill {\bf Summer 2025} }
                \vspace{2mm}
                \hbox to 6.28in { \hfill {\Large Module #1 Assignment} \hfill }
                \vspace{2mm}
                \hbox to 6.28in { Name: Sey Kim (No collaborators)  \hfill }

                \vspace{2mm}
            }
        }
    \end{center}
}



%---------Commands-----------
\renewcommand{\Pr}[1]{{\text{Pr}\left[ #1 \right]}}
\newcommand{\E}[1]{{\mathbf{E}\left[ #1 \right]}}
\newcommand{\Var}[1]{{\text{Var}\left( #1 \right)}}
\DeclareMathOperator*{\argmax}{arg\,max}
\DeclareMathOperator*{\argmin}{arg\,min}
\allowdisplaybreaks

%------Solutions Toggle------
\newcommand{\sol}[1]{{\par{}\textit{Solution: } \par{} #1 \par{}}}
% \newcommand{\sol}[1]{}

%=============================== END PREAMBLE ===============================

% ============================ 

% THIS IS THE FINAL COPY!!! Jun 18, 2025 at 11:02 PM

% ================================


%============================ BEGIN DOCUMENT ================================

\begin{document}
\boxheader{5--6}

{\bf Important:} For this homework and all subsequent homeworks, whenever you are asked to design an algorithm, your solution should include a \textbf{clear English description} of the algorithm, a \textbf{proof of correctness}, and an \textbf{analysis} of the time complexity of your algorithm. Remember, your audience is a human, not a computer. This means you should not submit actual code (like Python or Java), and if you choose to include any pseudocode, it should be brief, clear, and accompanied by an English explanation of what it is doing. Insufficiently explained pseudocode can lead to TAs struggling to figure out what you intended, which may result in deductions.

\begin{enumerate}

	% Question 1
	\item You are given the adjacency-list representation of a directed acyclic graph $G=(V,E)$ with $n$ vertices and $m$ edges. Furthermore, for every vertex $v\in V$ with in-degree zero, you are given a number $value(v)$. For every vertex $v\in V$ with positive in-degree, we define
	\[value(v)=\sum_{(u,v)\in E} value(u),\]
	the sum of the values of all of $v$'s predecessors in the graph.
	
	For example, in the graph below, the gray vertices have in-degree zero, so their values determine the values of all other vertices in the graph.
	
	\[\begin{tikzpicture}[scale=0.75]
			\tikzstyle{every node}+=[draw,thick,circle,inner sep=0pt,minimum size=20]
			\node[fill=lightgray] (a) at (0,0)  {$3$};
			\node (b) at (3,0)  {$6$};
			\node (c) at (6,0)  {$13$};
			\node[fill=lightgray] (d) at (9,0) {$5$};
			\node (e) at (12,0)  {$7$};
			\node (f) at (0,3)  {$3$};
			\node (g) at (3,3) {$3$};
			\node (h) at (6,3) {$3$};
			\node (i) at (9,3) {$10$};
			\node[fill=lightgray] (j) at (12,3)  {$2$};
			\tikzstyle{every edge}+=[->,thick]
			\draw (g) edge (h);
			\draw (h) edge (c);
			\draw (d) edge (i);
			\draw (j) edge (i);
			\draw (f) edge (g);
			\draw (h) edge (i);
			\draw (g) edge (b);
			\draw (a) edge (b) edge (f);
			\draw (d) edge (e);
			\draw (j) edge (e);
			\draw (i) edge (c);
			\end{tikzpicture}\]
			
	Design an $O(n+m)$-time algorithm to compute $value(v)$ for all vertices $v\in V$.~ \hfill{({\bf 20 points})}

	\sol{
	\textbf{Algorithm.}
	\begin{enumerate}
	  \item \emph{Compute in-degrees.}  Initialise $\deg[v]=0$ for all $v$; for each edge $(u,v)\in E$ set $\deg[v]\!:=\!\deg[v]+1$.
	  \item \emph{Initialise queue.}  Enqueue every source vertex $s$ with $\deg[s]=0$ and set $\mathrm{val}[s]\!:=\!value(s)$ (given).
	  \item \emph{Topological Sort}  While the queue is non-empty:
	        \begin{enumerate}
	          \item dequeue $u$;
	          \item for each outgoing edge $(u,v)\in E$ do
	                \[
	                  \mathrm{val}[v]\!:=\!\mathrm{val}[v]+\mathrm{val}[u],\quad
	                  \deg[v]\!:=\!\deg[v]-1;
	                \]
	                if $\deg[v]=0$ enqueue $v$.
	        \end{enumerate}
	\end{enumerate}

	\textbf{Proof of Correctness.}
	The dequeue order is a topological order.  
	\emph{Base step:} every source $s$ is assigned its given value, so $\mathrm{val}[s]=value(s)$.  
	\emph{Inductive step:} assume $\mathrm{val}[u]=value(u)$ for all predecessors $u$ processed before $v$.  
	When $v$ is dequeued,
	\[
	  \mathrm{val}[v]=\sum_{(u,v)\in E}\mathrm{val}[u]=\sum_{(u,v)\in E}value(u)=value(v).
	\]
	By induction this holds for every vertex; the algorithm therefore returns all correct values.

	\textbf{Time Complexity.}
	Steps 1 and 3 each scan every edge once and visit every vertex once, so the total running time is $O(n+m)$.  
	\textbf{Hence }$value(v)$ is computed in $O(n+m)$ time (and this bound is tight, i.e.\ $T(n,m)=\Theta(n+m)$).
	}



	% Question 2
    \item Suppose you are given the adjacency-list representation of a directed graph $G=(V,E)$ with $n$ vertices and $m$ edges, and you want to find both the number of sources and the number of sinks in $SCC(G)$, the DAG of $G$'s strongly connected components. Design an $O(n+m)$-time algorithm for this task. ~\hfill{({\bf 20 points})} 

	\sol{ 
	\textbf{Algorithm.}
	\begin{enumerate}
	  \item Run \emph{Kosaraju's algorithm} to label each vertex with its component id $\mathrm{comp}[v]\in\{1,\dots,k\}$.
	  \item Initialise arrays $\textit{in}[1..k]=0$ and $\textit{out}[1..k]=0$.
	  \item For every edge $(u,v)\in E$ with $\mathrm{comp}[u]\neq\mathrm{comp}[v]$ do  
	        $\textit{out}[\mathrm{comp}[u]]{+}{=}1,\;\textit{in}[\mathrm{comp}[v]]{+}{=}1$.
	  \item Compute  
	        $\textit{sources}=\#\{i\mid\textit{in}[i]=0\},\;
	          \textit{sinks}=\#\{i\mid\textit{out}[i]=0\}$.
	\end{enumerate}

	\textbf{Proof of Correctness.}
	Kosaraju partitions $V$ into its $k$ SCCs, so $\mathrm{comp}[v]$ is correct.  
	For each cross-component edge the algorithm records one outgoing edge of its tail SCC and one incoming edge of its head SCC, hence after Step 3 every $\textit{in}[i]$ and $\textit{out}[i]$ equals the true in- and out-degree of component $i$ in $SCC(G)$.  
	An SCC is a source (sink) iff its in-degree (out-degree) is zero, so Step 4 returns the exact counts.

	\textbf{Time Complexity.}
	Kosaraju's two DFS passes run in $O(n+m)$.  
	Step 3 scans all $m$ edges once; Step 4 scans $k\le n$ components.  
	Total time $O(n+m)$ and space $O(n)$.
	}




	% Question 3
    \item The \textbf{diameter} of a graph is defined as the maximum, over all pairs $(u,v)$ of vertices, of the distance from $u$ to $v$ in that graph. Design an algorithm that, given the adjacency-list representation of any undirected tree graph $T$ with $n$ vertices and $m$ edges, finds the diameter of $T$ in $O(n)$ time. ~\hfill{({\bf 20 points})} 

	\sol{
	\textbf{Algorithm.}
	\begin{enumerate}
	  \item Choose an arbitrary vertex $s$ (indexing the array 0 … $n\!-\!1$).
	  \item Run BFS from $s$; let $u$ be the vertex with maximum level.
	  \item Run BFS from $u$; let $v$ be the vertex with maximum level and record $D=\mathrm{dist}(u,v)$.
	  \item Output $D$ as the diameter.
	\end{enumerate}

	\textbf{Proof of Correctness.}
	Let $(x,y)$ be a pair realising the diameter $\mathrm{diam}(T)$.  
	In a tree, for any start vertex the farthest vertex is an endpoint of the diameter path.  
	BFS$_1$ therefore returns $u\in\{x,y\}$.  
	BFS$_2$ computes all distances from $u$, and its maximum is exactly $\mathrm{dist}(u,y)=\mathrm{diam}(T)$.  
	Hence the algorithm outputs the correct diameter.

	\textbf{Time Complexity.}
	A BFS on a tree with $m=n-1$ edges runs in $O(n)$.  
	Two BFS traversals give total time $O(n)$ and space $O(n)$.
	}


	
	% Question 4
    \item You are driving an electric car from New York to San Francisco along Interstate 80, and you know the locations of the charging stations along the way. You are given the locations of the charging stations as an array, sorted in increasing order, of distances from New York. After a charge, your car has a range of $R$ miles.  Assume that for any charging station, the next one is within distance $R$; so you will be able to complete your journey. Design an $O(n)$-time algorithm that finds the minimum number of charging stations you must stop at.

	\sol{
	\textbf{Algorithm.}
	Let the station mile-markers be $a_1<\dots<a_n$, with $a_0=0$ (start) and $a_{n+1}=D$ (destination).
	\begin{enumerate}
	  \item Set $\textit{reach}=R$, \textit{stops}=0.
	  \item For $i=1$ to $n+1$:
	        \begin{enumerate}
	          \item If $a_i>\textit{reach}$ then  
	                $\textit{stops}\!:=\!\textit{stops}+1,\;
	                \textit{reach}\!:=\!a_{i-1}+R$.
	        \end{enumerate}
	  \item Return \textit{stops}.
	\end{enumerate}

	\textbf{Proof of Correctness.}
	The algorithm always stops at the \emph{farthest} station reachable with the current charge.  
	Let "coverage" after $k$ stops be the farthest point any strategy with $k$ stops can reach.  
	The greedy choice maximises coverage at every stop, so after each $k$ it is at least as far as any other feasible strategy.  
	If another strategy used fewer stops than greedy, its coverage would eventually be overtaken, contradicting its ability to finish.  
	Therefore greedy uses the minimum possible number of stops.

	\textbf{Time Complexity.}
	The loop visits each of the $(n+1)$ positions once; all operations inside are $O(1)$.  
	Hence total running time $O(n)$ and $O(1)$ extra space.
	}
    
    ~\hfill{({\bf 20 points})}

	% Question 5
    \item Suppose you are given a binary code $\gamma$ for an alphabet $\Sigma$ that has $n$ symbols. You are given this as an array $A$ of length $n$, where each entry is an ordered pair $(x,\gamma(x))$, where $x$ is a symbol in the alphabet and $\gamma(x)$ is a binary string, the codeword for $x$. Let $L=\sum_{x\in\Sigma}|\gamma(x)|$ be the sum of all codeword lengths.

    Design an $O(L)$-time algorithm that determines if $\gamma$ is a prefix code.~\hfill{({\bf 20 points})}

	\sol {
	\textbf{Algorithm.}
	Insert each codeword into a binary trie.
	\begin{enumerate}
	  \item Create an empty root node.
	  \item For each $(x,\gamma(x))\in A$:
	        \begin{enumerate}
	          \item Let $w=\gamma(x)$ and start at the root.
	          \item For each bit $b$ of $w$:
	                \begin{enumerate}
	                  \item If current node is \texttt{leaf}, \textbf{reject} (prefix hit).
	                  \item Follow/create the child labelled $b$.
	                \end{enumerate}
	          \item After the last bit, at node $v$:
	                \begin{enumerate}
	                  \item If $v$ already has a child, \textbf{reject} ($w$ is prefix of earlier code).
	                  \item Mark $v$ as \texttt{leaf}.
	                \end{enumerate}
	        \end{enumerate}
	  \item If all insertions finish without rejection, \textbf{accept}.
	\end{enumerate}

	\textbf{Proof of Correctness.}
	\emph{Soundness:} A rejection occurs only if (i) a previously inserted codeword ends at a node encountered while inserting $w$, or (ii) $w$ ends at a node that already has descendants. In either case one codeword is a prefix of another, so $\gamma$ is not prefix-free.  
	\emph{Completeness:} If the algorithm accepts, no rejection occurred; thus for any two distinct codewords their paths diverge before either terminates, so none is a prefix of another. Hence $\gamma$ is prefix-free.

	\textbf{Time Complexity.}
	Each bit causes one constant-time trie action; total work is $\sum_{x\in\Sigma}|\gamma(x)|=L$.  
	Thus the algorithm runs in $O(L)$ time and uses $O(L)$ space.
	}


\end{enumerate}     

\end{document}