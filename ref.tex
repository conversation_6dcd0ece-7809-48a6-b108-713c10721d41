\documentclass[twoside,11pt]{article}

%================================ PREAMBLE ==================================

%--------- Packages -----------
\usepackage{fullpage}
\usepackage{amssymb}
\usepackage{amsmath}
\usepackage{amsthm}
\usepackage{latexsym}
\usepackage{clrscode3e}
\usepackage{graphicx}
\usepackage{color}
\usepackage{url}
\usepackage{tikz}
\usepackage{listings}
\usepackage{fancyhdr}
\lstset{ basicstyle=\ttfamily, columns=fullflexible, frame=single, breaklines=true, postbreak=\mbox{\textcolor{red}{$\rightarrow$}\space} }

%----------Spacing-------------
\setlength{\topmargin}{-0.6 in}
\setlength{\headsep}{0.75 in}
\setlength{\parindent}{0 in}
\setlength{\parskip}{0.1 in}
\setlength{\headheight}{13.6pt}

\newcommand{\boxheader}[1]{
    \pagestyle{myheadings}
    \thispagestyle{plain}
    \newpage
    \setcounter{page}{1}
    \noindent
    \begin{center}
        \framebox{
            \vbox{
                \vspace{2mm}
                \hbox to 6.28in { {\bf CIT 596 Online} \hfill {\bf Summer 2025} }
                \vspace{2mm}
                \hbox to 6.28in { \hfill {\Large Module #1 Assignment} \hfill }
                \vspace{2mm}
                \hbox to 6.28in { Name: Sey Kim (No collaborators)  \hfill }

                \vspace{2mm}
            }
        }
    \end{center}
}



%---------Commands-----------
\renewcommand{\Pr}[1]{{\text{Pr}\left[ #1 \right]}}
\newcommand{\E}[1]{{\mathbf{E}\left[ #1 \right]}}
\newcommand{\Var}[1]{{\text{Var}\left( #1 \right)}}
\DeclareMathOperator*{\argmax}{arg\,max}
\DeclareMathOperator*{\argmin}{arg\,min}
\allowdisplaybreaks

%------Solutions Toggle------
\newcommand{\sol}[1]{{\par{}\textit{Solution: } \par{} #1 \par{}}}
% \newcommand{\sol}[1]{}

%=============================== END PREAMBLE ===============================

% ============================ 

% THIS IS THE FINAL COPY!!! Jun 18, 2025 at 11:02 PM

% ================================


%============================ BEGIN DOCUMENT ================================

\begin{document}
\boxheader{5--6}

{\bf Important:} For this homework and all subsequent homeworks, whenever you are asked to design an algorithm, your solution should include a \textbf{clear English description} of the algorithm, a \textbf{proof of correctness}, and an \textbf{analysis} of the time complexity of your algorithm. Remember, your audience is a human, not a computer. This means you should not submit actual code (like Python or Java), and if you choose to include any pseudocode, it should be brief, clear, and accompanied by an English explanation of what it is doing. Insufficiently explained pseudocode can lead to TAs struggling to figure out what you intended, which may result in deductions.

\begin{enumerate}

	% Question 1
	\item You are given the adjacency-list representation of a directed acyclic graph $G=(V,E)$ with $n$ vertices and $m$ edges. Furthermore, for every vertex $v\in V$ with in-degree zero, you are given a number $value(v)$. For every vertex $v\in V$ with positive in-degree, we define
	\[value(v)=\sum_{(u,v)\in E} value(u),\]
	the sum of the values of all of $v$'s predecessors in the graph.
	
	For example, in the graph below, the gray vertices have in-degree zero, so their values determine the values of all other vertices in the graph.
	
	\[\begin{tikzpicture}[scale=0.75]
			\tikzstyle{every node}+=[draw,thick,circle,inner sep=0pt,minimum size=20]
			\node[fill=lightgray] (a) at (0,0)  {$3$};
			\node (b) at (3,0)  {$6$};
			\node (c) at (6,0)  {$13$};
			\node[fill=lightgray] (d) at (9,0) {$5$};
			\node (e) at (12,0)  {$7$};
			\node (f) at (0,3)  {$3$};
			\node (g) at (3,3) {$3$};
			\node (h) at (6,3) {$3$};
			\node (i) at (9,3) {$10$};
			\node[fill=lightgray] (j) at (12,3)  {$2$};
			\tikzstyle{every edge}+=[->,thick]
			\draw (g) edge (h);
			\draw (h) edge (c);
			\draw (d) edge (i);
			\draw (j) edge (i);
			\draw (f) edge (g);
			\draw (h) edge (i);
			\draw (g) edge (b);
			\draw (a) edge (b) edge (f);
			\draw (d) edge (e);
			\draw (j) edge (e);
			\draw (i) edge (c);
			\end{tikzpicture}\]
			
	Design an $O(n+m)$-time algorithm to compute $value(v)$ for all vertices $v\in V$.~ \hfill{({\bf 20 points})}

	\sol{
	\textbf{Algorithm.}
	\begin{enumerate}
	  \item \emph{Compute in-degrees.}  Initialize $\deg[v]=0$ and $\mathrm{val}[v]=0$ for all $v$; for each edge $(u,v)\in E$ set $\deg[v]\!:=\!\deg[v]+1$.
	  \item \emph{initialize queue.}  Enqueue every source vertex $s$ with $\deg[s]=0$ and set $\mathrm{val}[s]\!:=\!value(s)$ (given).
	  \item \emph{Topological Sort}  While the queue is non-empty:
	        \begin{enumerate}
	          \item dequeue $u$;
	          \item for each outgoing edge $(u,v)\in E$ do
	                \[
	                  \mathrm{val}[v]\!:=\!\mathrm{val}[v]+\mathrm{val}[u],\quad
	                  \deg[v]\!:=\!\deg[v]-1;
	                \]
	                if $\deg[v]=0$ enqueue $v$.
	        \end{enumerate}
	\end{enumerate}

	\textbf{Proof of Correctness.}
	The algorithm processes vertices in a topological order. A vertex $v$ is only enqueued (and later dequeued) when its in-degree counter becomes zero. This only happens after all its predecessors have been dequeued and processed, which is the definition of a topological sort.

	The inductive hypothesis is that when any vertex $u$ is dequeued, the value $\mathrm{val}[u]$ has been correctly computed. \emph{Base Case:} This is true for all source vertices, which are placed on the queue initially with their correct given values. \emph{Inductive Step:} Consider a non-source vertex $v$. By the property of the topological sort, $v$ is processed only after all its predecessors $u_1, \ldots, u_k$ have been processed. By the inductive hypothesis, $\mathrm{val}[u_i] = value(u_i)$ for all these predecessors. Our algorithm computes $\mathrm{val}[v]$ by summing $\mathrm{val}[u_i]$ for each incoming edge $(u_i, v)$. Since $\mathrm{val}[v]$ was initialized to 0, the final sum is $\mathrm{val}[v] = \sum_{(u,v)\in E} \mathrm{val}[u] = \sum_{(u,v)\in E} value(u)$, which is the correct $value(v)$ by definition. The induction holds.

	\textbf{Time Complexity.}
	Steps 1 and 3 each scan every edge once and visit every vertex once, so the total running time is $O(n+m)$.  
	Hence $value(v)$ is computed in $O(n+m)$ time (and this bound is tight, i.e.\ $T(n,m)=\Theta(n+m)$).
	}



	% Question 2
    \item Suppose you are given the adjacency-list representation of a directed graph $G=(V,E)$ with $n$ vertices and $m$ edges, and you want to find both the number of sources and the number of sinks in $SCC(G)$, the DAG of $G$'s strongly connected components. Design an $O(n+m)$-time algorithm for this task. ~\hfill{({\bf 20 points})} 

	\sol{ 
	\textbf{Algorithm.}
	\begin{enumerate}
	  \item Run \emph{Kosaraju's algorithm} to label each vertex with its component id $\mathrm{comp}[v]\in\{1,\dots,k\}$.
	  \item initialize arrays $\textit{in}[1..k]=0$ and $\textit{out}[1..k]=0$.
	  \item For every edge $(u,v)\in E$ with $\mathrm{comp}[u]\neq\mathrm{comp}[v]$ do  
	        $\textit{out}[\mathrm{comp}[u]]{+}{=}1,\;\textit{in}[\mathrm{comp}[v]]{+}{=}1$.
	  \item Compute  
	        $\textit{sources}=\#\{i\mid\textit{in}[i]=0\},\;
	          \textit{sinks}=\#\{i\mid\textit{out}[i]=0\}$.
	\end{enumerate}

	\textbf{Proof of Correctness.}
	Kosaraju partitions $V$ into its $k$ SCCs, so $\mathrm{comp}[v]$ is correct.
	The algorithm correctly computes the in- and out-degrees of each component in the condensation graph $SCC(G)$. An edge $(U, V)$ exists in $SCC(G)$ if and only if there is an edge $(u, v)$ in $G$ such that $u \in U$ and $v \in V$. Step 3 correctly counts these edges for each component.
	For each cross-component edge the algorithm records one outgoing edge of its tail SCC and one incoming edge of its head SCC, hence after Step 3 every $\textit{in}[i]$ and $\textit{out}[i]$ equals the true in- and out-degree of component $i$ in $SCC(G)$.
	An SCC is a source (sink) iff its in-degree (out-degree) is zero, so Step 4 returns the exact counts.

	\textbf{Time Complexity.}
	Kosaraju's two DFS passes run in $O(n+m)$.  
	Step 3 scans all $m$ edges once; Step 4 scans $k\le n$ components.  
	Total time $O(n+m)$ and space $O(n)$.
	}




	% Question 3
    \item The \textbf{diameter} of a graph is defined as the maximum, over all pairs $(u,v)$ of vertices, of the distance from $u$ to $v$ in that graph. Design an algorithm that, given the adjacency-list representation of any undirected tree graph $T$ with $n$ vertices and $m$ edges, finds the diameter of $T$ in $O(n)$ time. ~\hfill{({\bf 20 points})} 

	\sol{
	\textbf{Algorithm.}
	\begin{enumerate}
	  \item Choose an arbitrary vertex $s$ (indexing the array 0 … $n\!-\!1$).
	  \item Run BFS from $s$; let $u$ be the vertex with maximum level.
	  \item Run BFS from $u$; let $v$ be the vertex with maximum level and record $D=\mathrm{dist}(u,v)$.
	  \item Output $D$ as the diameter.
	\end{enumerate}

	\textbf{Proof of Correctness.}
	Let $(x,y)$ be a pair realising the diameter $\mathrm{diam}(T)$.
	We first prove that for any start vertex $s$, the vertex $u$ farthest from $s$ must be an endpoint of some diameter path. Let $P_{xy}$ be the path between $x$ and $y$, and $P_{su}$ be the path between $s$ and $u$. Let $t$ be the vertex where these two paths first meet. We have $\mathrm{dist}(s,u) = \mathrm{dist}(s,t) + \mathrm{dist}(t,u)$ and $\mathrm{dist}(s,x) = \mathrm{dist}(s,t) + \mathrm{dist}(t,x)$. By our choice of $u$, we know $\mathrm{dist}(s,u) \ge \mathrm{dist}(s,x)$, which implies $\mathrm{dist}(t,u) \ge \mathrm{dist}(t,x)$. Now consider $\mathrm{dist}(y,u) = \mathrm{dist}(y,t) + \mathrm{dist}(t,u) \ge \mathrm{dist}(y,t) + \mathrm{dist}(t,x) = \mathrm{dist}(y,x) = \mathrm{diam}(T)$. Since the diameter is the maximum distance, we have $\mathrm{dist}(y,u) = \mathrm{diam}(T)$, so $u$ is an endpoint of a diameter path.

	BFS1 therefore returns $u\in\{x,y\}$.
	BFS2 computes all distances from $u$, and its maximum is exactly $\mathrm{dist}(u,y)=\mathrm{diam}(T)$.
	Hence the algorithm outputs the correct diameter.

	\textbf{Time Complexity.}
	A BFS on a tree with $m=n-1$ edges runs in $O(n)$.  
	Two BFS traversals give total time $O(n)$ and space $O(n)$.
	}


	
	% Question 4
    \item You are driving an electric car from New York to San Francisco along Interstate 80, and you know the locations of the charging stations along the way. You are given the locations of the charging stations as an array, sorted in increasing order, of distances from New York. After a charge, your car has a range of $R$ miles.  Assume that for any charging station, the next one is within distance $R$; so you will be able to complete your journey. Design an $O(n)$-time algorithm that finds the minimum number of charging stations you must stop at.

	\sol{
	\textbf{Algorithm.}
	Let the station mile-markers be $a_1<\dots<a_n$, with $a_0=0$ (start) and $a_{n+1}=D$ (destination).
	\begin{enumerate}
	  \item Set $\textit{reach}=R$, \textit{stops}=0.
	  \item For $i=1$ to $n+1$:
	        \begin{enumerate}
	          \item If $a_i>\textit{reach}$ then  
	                $\textit{stops}\!:=\!\textit{stops}+1,\;
	                \textit{reach}\!:=\!a_{i-1}+R$.
	        \end{enumerate}
	  \item Return \textit{stops}.
	\end{enumerate}

	\textbf{Proof of Correctness.}
	Let the greedy strategy's stops be $g_1, g_2, \ldots, g_k$ and an optimal strategy's stops be $o_1, o_2, \ldots, o_p$. We want to show $k=p$. The greedy choice is to travel as far as possible from the current location. The greedy choice for the first stop, $g_1$, is the farthest station from the start. Any optimal strategy's first stop, $o_1$, must be at or before $g_1$ (i.e., $a_{o_1} \le a_{g_1}$). Therefore, the reach from the greedy stop is at least as good as the reach from the optimal stop: $a_{g_1} + R \ge a_{o_1} + R$.

	\emph{Using Inductive Hypothesis:} Assume that after $i$ stops, the greedy strategy's reach is at least as far as any optimal strategy's reach. That is, $a_{g_i} + R \ge a_{o_i} + R$. \emph{Inductive Step:} Consider the $(i+1)$-th stop. The greedy strategy picks the farthest reachable station $g_{i+1}$ from $g_i$. The optimal strategy picks $o_{i+1}$ from $o_i$. Since the greedy strategy could start its leg from a point at least as far as the optimal one ($a_{g_i} \ge a_{o_i}$), its next chosen stop $g_{i+1}$ will also be at least as far as $o_{i+1}$. Thus, the greedy strategy always 'stays ahead'. Since the greedy strategy successfully reaches the destination, and at every step it is at least as advanced as any optimal strategy, it must use at most the same number of stops. Since the optimal strategy uses the minimum number of stops by definition, the greedy strategy must also be optimal and use the minimum number of stops.

	\textbf{Time Complexity.}
	The loop visits each of the $(n+1)$ positions once; all operations inside are $O(1)$.  
	Hence total running time $O(n)$ and $O(1)$ extra space.
	}
    
    ~\hfill{({\bf 20 points})}

	% Question 5
    \item Suppose you are given a binary code $\gamma$ for an alphabet $\Sigma$ that has $n$ symbols. You are given this as an array $A$ of length $n$, where each entry is an ordered pair $(x,\gamma(x))$, where $x$ is a symbol in the alphabet and $\gamma(x)$ is a binary string, the codeword for $x$. Let $L=\sum_{x\in\Sigma}|\gamma(x)|$ be the sum of all codeword lengths.

    Design an $O(L)$-time algorithm that determines if $\gamma$ is a prefix code.~\hfill{({\bf 20 points})}

	\sol {
	\textbf{Algorithm.}
	Insert each codeword into a binary trie.
	\begin{enumerate}
	  \item Create an empty root node.
	  \item For each $(x,\gamma(x))\in A$:
	        \begin{enumerate}
	          \item Let $w=\gamma(x)$ and start at the root.
	          \item For each bit $b$ of $w$:
	                \begin{enumerate}
	                  \item If current node is \texttt{leaf}, \textbf{reject} (prefix hit).
	                  \item Follow/create the child labelled $b$.
	                \end{enumerate}
	          \item After the last bit, at node $v$:
	                \begin{enumerate}
	                  \item If $v$ already has a child, \textbf{reject} ($w$ is prefix of earlier code).
	                  \item Mark $v$ as \texttt{leaf}.
	                \end{enumerate}
	        \end{enumerate}
	  \item If all insertions finish without rejection, \textbf{accept}.
	\end{enumerate}

	\textbf{Proof of Correctness.}
	If the algorithm rejects, it is because one of two conditions was met: (1) While traversing the trie for a codeword $w$, we encounter a node that was marked as a \texttt{leaf}. This means the path for a previously inserted, shorter codeword $w'$ is a prefix of the path for $w$. (2) After successfully inserting the full path for $w$ ending at node $v$, we find that $v$ already has children. This means a previously inserted, longer codeword $w''$ has a path that goes through $v$. Therefore, $w$ is a prefix of $w''$. In either case, one codeword is a prefix of another, so the code is not a prefix code.
	For Completeness: If the algorithm accepts, it means that for any two distinct codewords $w_1$ and $w_2$, the path for $w_1$ did not end on a node that is an ancestor of $w_2$'s end-node, and vice-versa. This is the definition of a prefix code.

	\textbf{Time Complexity.}
	Each bit causes one constant-time trie action; total work is $\sum_{x\in\Sigma}|\gamma(x)|=L$.  
	Thus the algorithm runs in $O(L)$ time and uses $O(L)$ space.
	}


\end{enumerate}     

\end{document}