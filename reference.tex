\documentclass[twoside,11pt]{article}

%================================ PREAMBLE ==================================

%--------- Packages -----------
\usepackage{fullpage}
\usepackage{amssymb}
\usepackage{amsmath}
\usepackage{amsthm}
\usepackage{latexsym}
\usepackage{clrscode3e}
\usepackage{graphicx}
\usepackage{color}
\usepackage{url}
\usepackage{tikz}
\usepackage{listings}
\usepackage{fancyhdr}
\lstset{ basicstyle=\ttfamily, columns=fullflexible, frame=single, breaklines=true, postbreak=\mbox{\textcolor{red}{$\rightarrow$}\space} }

%----------Spacing-------------
\setlength{\topmargin}{-0.6 in}
\setlength{\headsep}{0.75 in}
\setlength{\parindent}{0 in}
\setlength{\parskip}{0.1 in}
\setlength{\headheight}{13.6pt}

\newcommand{\boxheader}[1]{
    \pagestyle{myheadings}
    \thispagestyle{plain}
    \newpage
    \setcounter{page}{1}
    \noindent
    \begin{center}
        \framebox{
            \vbox{
                \vspace{2mm}
                \hbox to 6.28in { {\bf CIT 596 Online} \hfill {\bf Summer 2025} }
                \vspace{2mm}
                \hbox to 6.28in { \hfill {\Large Module #1 Assignment} \hfill }
                \vspace{2mm}
                \hbox to 6.28in { Name: Sey Kim - No collaborators \hfill }

                \vspace{2mm}
            }
        }
    \end{center}
}



%---------Commands-----------
\renewcommand{\Pr}[1]{{\text{Pr}\left[ #1 \right]}}
\newcommand{\E}[1]{{\mathbf{E}\left[ #1 \right]}}
\newcommand{\Var}[1]{{\text{Var}\left( #1 \right)}}
\DeclareMathOperator*{\argmax}{arg\,max}
\DeclareMathOperator*{\argmin}{arg\,min}
\allowdisplaybreaks

%------Solutions Toggle------
\newcommand{\sol}[1]{{\par{}\textit{Solution: } \par{} #1 \par{}}}
% \newcommand{\sol}[1]{}

%=============================== END PREAMBLE ===============================

%============================ BEGIN DOCUMENT ================================

\begin{document}
\boxheader{5--6}

{\bf Important:} For this homework and all subsequent homeworks, whenever you are asked to design an algorithm, your solution should include a \textbf{clear English description} of the algorithm, a \textbf{proof of correctness}, and an \textbf{analysis} of the time complexity of your algorithm. Remember, your audience is a human, not a computer. This means you should not submit actual code (like Python or Java), and if you choose to include any pseudocode, it should be brief, clear, and accompanied by an English explanation of what it is doing. Insufficiently explained pseudocode can lead to TAs struggling to figure out what you intended, which may result in deductions.

\begin{enumerate}

	\item You are given the adjacency-list representation of a directed acyclic graph $G=(V,E)$ with $n$ vertices and $m$ edges. Furthermore, for every vertex $v\in V$ with in-degree zero, you are given a number $value(v)$. For every vertex $v\in V$ with positive in-degree, we define
	\[value(v)=\sum_{(u,v)\in E} value(u),\]
	the sum of the values of all of $v$'s predecessors in the graph.
	
	For example, in the graph below, the gray vertices have in-degree zero, so their values determine the values of all other vertices in the graph.
	
	\[\begin{tikzpicture}[scale=0.75]
			\tikzstyle{every node}+=[draw,thick,circle,inner sep=0pt,minimum size=20]
			\node[fill=lightgray] (a) at (0,0)  {$3$};
			\node (b) at (3,0)  {$6$};
			\node (c) at (6,0)  {$13$};
			\node[fill=lightgray] (d) at (9,0) {$5$};
			\node (e) at (12,0)  {$7$};
			\node (f) at (0,3)  {$3$};
			\node (g) at (3,3) {$3$};
			\node (h) at (6,3) {$3$};
			\node (i) at (9,3) {$10$};
			\node[fill=lightgray] (j) at (12,3)  {$2$};
			\tikzstyle{every edge}+=[->,thick]
			\draw (g) edge (h);
			\draw (h) edge (c);
			\draw (d) edge (i);
			\draw (j) edge (i);
			\draw (f) edge (g);
			\draw (h) edge (i);
			\draw (g) edge (b);
			\draw (a) edge (b) edge (f);
			\draw (d) edge (e);
			\draw (j) edge (e);
			\draw (i) edge (c);
			\end{tikzpicture}\]
			
	Design an $O(n+m)$-time algorithm to compute $value(v)$ for all vertices $v\in V$.~ \hfill{({\bf 20 points})}

	\sol{\textbf{Algorithm.}
\begin{enumerate}
  \item \emph{Compute in-degrees.}  
        Initialise $\deg[v]=0$ for every $v\in V$; for each edge $(u,v)\in E$ set $\deg[v]\gets\deg[v]+1$.
  \item \emph{Initialise queue.}  
        Enqueue every source vertex $s$ with $\deg[s]=0$ and set $\mathrm{val}[s]\gets\textit{value}(s)$ (given).
  \item \emph{Kahn's sweep (aka Topological sort in lecture).}\, While the queue is non-empty:  
        \begin{enumerate}
          \item Dequeue $u$.
          \item For each edge $(u,v)\in E$ do
                $$\mathrm{val}[v]\gets\mathrm{val}[v]+\mathrm{val}[u],\qquad\deg[v]\gets\deg[v]-1.$$
                If $\deg[v]=0$, enqueue $v$.
        \end{enumerate}
\end{enumerate}
When the queue empties, every vertex $v$ has $\mathrm{val}[v]=\textit{value}(v)$.

\textbf{Proof of Correctness.}  
Process order is a topological order produced by Kahn's algorithm.  
\emph{Base case:} Each source $s$ is assigned the provided $\textit{value}(s)$, so $\mathrm{val}[s]=\textit{value}(s)$.  
\emph{Inductive step:} Assume every vertex dequeued before $v$ already satisfies $\mathrm{val}[u]=\textit{value}(u)$.  
All predecessors of $v$ are among these earlier vertices; thus when $v$ is dequeued
$$
\mathrm{val}[v]=\sum_{(u,v)\in E}\mathrm{val}[u]=\sum_{(u,v)\in E}\textit{value}(u)=\textit{value}(v).
$$
By induction, the equality holds for all vertices, proving correctness.

\textbf{Analysis.}  
\emph{Time:} Step 1 scans every edge once; Step 3 scans each edge once more and visits each vertex once.  
Total running time is $O(n+m)$.  
\emph{Space:} The algorithm stores $\deg[v]$, $\mathrm{val}[v]$, and the queue, all $O(n)$, plus the given adjacency lists.}

% PROBLEM 2
    \item Suppose you are given the adjacency-list representation of a directed graph $G=(V,E)$ with $n$ vertices and $m$ edges, and you want to find both the number of sources and the number of sinks in $SCC(G)$, the DAG of $G$'s strongly connected components. Design an $O(n+m)$-time algorithm for this task. ~\hfill{({\bf 20 points})} 

	\sol{
		\textbf{Algorithm.}
\begin{enumerate}
  \item \emph{Find strongly connected components.}  
        Run Kosaraju’s two-DFS procedure; let $\mathrm{comp}[v]\in\{1,\dots ,k\}$ be the index of the SCC containing vertex $v$.
  \item \emph{Compute in/out degrees in the condensation DAG.}  
        Initialise arrays $\textit{in}[1..k]=0$ and $\textit{out}[1..k]=0$.  
        For every edge $(u,v)\in E$:
        \[
          \text{if }\mathrm{comp}[u]\neq\mathrm{comp}[v]\text{ then } 
          \textit{out}[\mathrm{comp}[u]]\!+\!=1,\;
          \textit{in}[\mathrm{comp}[v]]\!+\!=1.
        \]
  \item \emph{Count sources and sinks.}  
        \[
          \textit{sources}=\#\{i\mid\textit{in}[i]=0\},\qquad
          \textit{sinks}=\#\{i\mid\textit{out}[i]=0\}.
        \]
        Return both numbers.
\end{enumerate}

\textbf{Proof of Correctness.}  
Kosaraju’s algorithm correctly partitions $V$ into its maximally strongly connected subsets, so $\mathrm{comp}[v]$ is accurate for every vertex.  
If an edge $(u,v)$ has $\mathrm{comp}[u]\neq\mathrm{comp}[v]$, it induces exactly one edge from the SCC of $u$ to that of $v$ in $SCC(G)$; the algorithm records this by incrementing the out-degree of the tail component and the in-degree of the head component.  
Consequently $\textit{in}[i]$ and $\textit{out}[i]$ equal the true in- and out-degrees of component $i$ in $SCC(G)$.  
A component is a \emph{source} iff its in-degree is $0$ and a \emph{sink} iff its out-degree is $0$, so the counts reported in Step 3 match exactly the number of sources and sinks in $SCC(G)$.

\textbf{Time Complexity.}  
Kosaraju’s two DFS traversals visit every vertex and edge once: $O(n+m)$.  
Scanning all $m$ edges to update degree counts costs $O(m)$.  
Counting over the $k\le n$ SCCs costs $O(n)$.  
Thus the total running time is $O(n+m)$, with $O(n)$ additional space beyond the adjacency lists.
	}

% Problem 3
    \item The \textbf{diameter} of a graph is defined as the maximum, over all pairs $(u,v)$ of vertices, of the distance from $u$ to $v$ in that graph. Design an algorithm that, given the adjacency-list representation of any undirected tree graph $T$ with $n$ vertices and $m$ edges, finds the diameter of $T$ in $O(n)$ time. ~\hfill{({\bf 20 points})} 

	\sol{
		\textbf{Algorithm.}
\begin{enumerate}
  \item \emph{First BFS sweep.}  
        Pick any vertex $s\in V$ and run Breadth-First Search.  
        Let $u$ be the vertex with largest BFS level (distance) from $s$.
  \item \emph{Second BFS sweep.}  
        Run BFS again, now starting from $u$.  
        Let $v$ be the vertex with largest level from $u$; record $D=\mathrm{dist}(u,v)$.
  \item \emph{Output.}  Return $D$ as the diameter of $T$.
\end{enumerate}

\textbf{Proof of Correctness.}
Let $(x,y)$ be a pair of vertices whose distance equals the diameter $\mathrm{diam}(T)$.  
In any tree, every path is unique, so for any starting vertex $s$ one endpoint of $\mathrm{diam}(T)$ is among the vertices farthest from $s$.  
Thus the first BFS returns $u\in\{x,y\}$.  
The second BFS computes exact shortest-path distances from $u$; its maximum value occurs at the other endpoint of the diameter, so $D=\mathrm{dist}(u,v)=\mathrm{diam}(T)$.  
Therefore the algorithm outputs the true diameter.

\textbf{Analysis.}
Each BFS visits every vertex once and scans each of the $m=n-1$ edges twice, taking $O(n)$ time and $O(n)$ space.  
Two BFS runs give total time $O(n)$ and space $O(n)$, meeting the required bound.
	}

	% PROBLEM 4
    \item You are driving an electric car from New York to San Francisco along Interstate 80, and you know the locations of the charging stations along the way. You are given the locations of the charging stations as an array, sorted in increasing order, of distances from New York. After a charge, your car has a range of $R$ miles.  Assume that for any charging station, the next one is within distance $R$; so you will be able to complete your journey. Design an $O(n)$-time algorithm that finds the minimum number of charging stations you must stop at.

	\sol{
		\textbf{Algorithm.}
Let the station positions be $a_1<\dots<a_n$, with $a_0=0$ (New York) and $a_{n+1}=D$ (San Francisco).  
Set $\textit{reach}=R,\;\textit{prev}=a_0,\;\textit{stops}=0$.

\begin{enumerate}
  \item For $i=1$ to $n+1$:
        \begin{enumerate}
          \item If $a_i>\textit{reach}$\,: \quad
                increment $\textit{stops}$; set $\textit{reach}=a_{i-1}+R$.
          \item (Now $\textit{reach}\ge a_i$.) Continue.
        \end{enumerate}
  \item Return $\textit{stops}$.
\end{enumerate}

\textbf{Proof of Correctness.}
Define the \emph{coverage} after $j$ stops as the farthest mile marker a strategy can reach.  
\emph{Greedy stays ahead:} At each decision point the algorithm stops at the \emph{farthest} station within the current charge, hence its coverage after every stop is at least that of any other feasible strategy with the same number of stops.  
Suppose an optimal solution uses fewer stops than the greedy one.  
Compare both strategies step-by-step: until they first differ, coverages are equal.  
At the first difference the greedy stop is farther than the optimal’s, so greedy coverage \emph{strictly} exceeds optimal coverage, contradicting the fact that the optimal strategy eventually reaches $D$.  
Therefore no solution uses fewer stops, and the algorithm is optimal.

\textbf{Analysis.}
The loop scans the $(n+1)$ positions once; each station triggers at most one constant-time update.  
Time complexity $O(n)$, extra space $O(1)$.
	}
    
    ~\hfill{({\bf 20 points})}

	% PROBLEM 5
    \item Suppose you are given a binary code $\gamma$ for an alphabet $\Sigma$ that has $n$ symbols. You are given this as an array $A$ of length $n$, where each entry is an ordered pair $(x,\gamma(x))$, where $x$ is a symbol in the alphabet and $\gamma(x)$ is a binary string, the codeword for $x$. Let $L=\sum_{x\in\Sigma}|\gamma(x)|$ be the sum of all codeword lengths.

    Design an $O(L)$-time algorithm that determines if $\gamma$ is a prefix code.~\hfill{({\bf 20 points})}

	\sol{\textbf{Problem 5 (interpreted).}  
Given an array $A=\bigl((x_1,\gamma(x_1)),\dots,(x_n,\gamma(x_n))\bigr)$ of $n$ binary codewords whose total length is  
$L=\sum_{i=1}^{n}|\gamma(x_i)|$, design an $O(L)$–time algorithm that decides whether the code $\gamma$ is \emph{prefix-free} (i.e.\ no codeword is the prefix of another).

\medskip
\textbf{Algorithm.}
Build a binary trie while scanning the codewords.

\begin{enumerate}
  \item Create a root node, initially unmarked as a leaf.
  \item For each codeword $w=\gamma(x_i)$:
        \begin{enumerate}
          \item Start at the root.  
          \item For every bit $b$ in $w$ (left to right):
                \begin{enumerate}
                  \item If the current node is already a \texttt{leaf}, \textbf{reject} (some earlier codeword is a prefix of $w$).
                  \item Follow / create the child edge labelled $b$.
                \end{enumerate}
          \item After the last bit, we are at node $v$.
                \begin{enumerate}
                  \item If $v$ has any child edge, \textbf{reject} ($w$ is a prefix of a previously inserted codeword).
                  \item Mark $v$ as \texttt{leaf}.
                \end{enumerate}
        \end{enumerate}
  \item After all insertions, \textbf{accept} (the code is prefix-free).
\end{enumerate}

\medskip
\textbf{Proof of Correctness.}
We prove both directions.

\emph{Soundness.}  
If the algorithm rejects there are two possible triggers:
(i) while inserting $w$ we encounter a node already marked as \texttt{leaf}; that earlier codeword is a prefix of $w$;
(ii) when $w$ finishes, node $v$ already has children, so $w$ is a prefix of some earlier, longer codeword.
In either case the code contains a prefix relation, so it is not prefix-free.

\emph{Completeness.}  
Assume the algorithm accepts.  
Then no rejection occurred, so for every pair of codewords $u,w$ their paths in the trie diverge strictly before either path reaches a \texttt{leaf}.  
Therefore neither codeword is a prefix of the other.  
Hence the code is prefix-free.

\medskip
\textbf{Analysis.}
Each bit of every codeword is processed exactly once, and each trie node and edge is created at most once.  
Thus the running time is $O(L)$ and the extra memory used by the trie is $O(L)$.}


\end{enumerate}     

\end{document}